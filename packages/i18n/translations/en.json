{"home": {"title": "AI Music & Song Generator", "desc": "Create unique music in a minute or less with our AI-Powered song generator. Free, fast & fun!"}, "songCoverSubmitted": "Music cover request submitted, processing...", "songCoverCompleted": "Music cover completed!", "songCoverFailed": "Music cover processing failed", "firstTrackGenerated": "First track generated, still processing...", "lyricsGenerated": "Lyrics generated, creating audio...", "processingTask": "Processing task...", "productionCompleted": "Production completed", "callbackException": "Error during callback processing", "audioGenerationFailed": "Failed to generate audio", "sensitiveWordError": "Content contains sensitive words, please modify and try again", "processingTracks": "Processing tracks...", "checkCoverFileFormat": "Audio generation failed", "errorCreateTask": "Failed to create task", "errorGenerateAudio": "Failed to generate audio", "errorCallback": "Error during callback processing", "errorSensitiveContent": "Content contains sensitive words, please modify and try again", "FaqSectionPro": {"faqData": {"a": {"question": "What is woodcarvings?", "answer": "woodcarvings is an advanced AI music generator that allows anyone to create songs effortlessly. Whether you need a song maker, AI song generator, or vocal remover, our platform provides powerful tools for music creation.\nWith woodcarvings, you can:", "featureTitle": "With woodcarvings, you can:", "feature1": "Generate full songs using AI lyrics generator and text-to-song technology.", "feature2": "Create instrumentals or use our free vocal remover to extract vocals.", "feature3": "Customize melodies and beats with our music maker for unique compositions.", "conclusion": "Perfect for content creators, musicians, and hobbyists, woodcarvings makes professional-quality music creation easier than ever!"}, "b": {"question": "How Does woodcarvings Work?", "answer": "woodcarvings makes AI-powered music creation easy for everyone. Here's how it works:", "feature1": "Choose Your Music Style – Select a genre, mood, or artist-inspired sound.", "feature2": "Generate Lyrics or Input Your Own – Use our AI lyrics generator or customize your own lyrics.", "feature3": "AI Song Creation – Our AI music generator composes a complete track, including melody, vocals, and instrumentals.", "feature4": "Customize & Edit – Adjust beats, vocals, or use our free vocal remover for an instrumental-only version.", "feature5": "Download & Share – Export your song and use it for videos, ringtones, or personal projects.", "conclusion": "Start creating your own music effortlessly with woodcarvings!"}, "c": {"question": "How can I remove vocals from a song on woodcarvings?", "answer": "Removing vocals from any song is simple with woodcarvings's free vocal remover:", "feature1": "Upload Your Song – Select the audio file you want to process.", "feature2": "AI Vocal Removal – Our AI-powered vocal remover will separate the instrumental and vocals with high precision.", "feature3": "Preview & Download – Listen to the extracted instrumental and download it in your preferred format.", "conclusion": "Use our free vocal remover to create karaoke tracks, remixes, or instrumentals effortlessly!"}, "d": {"question": "How can I create my own ringtone with woodcarvings's AI song generator?", "answer": "With woodcarvings, making a custom ringtone is quick and easy:", "feature1": "Generate a Song – Use our AI song generator to create a unique melody or upload your own lyrics for a personalized tune.", "feature2": "Customize Your Track – Adjust the tempo, style, and vocals to match your preferred ringtone sound.", "feature3": "Use the Vocal Remover (Optional) – If you want an instrumental ringtone, our free vocal remover can isolate the melody.", "feature4": "Download & Set as Ringtone – Export your final track in the desired format and set it as your phone's ringtone.", "conclusion": "Start creating your custom ringtone with woodcarvings today!"}, "e": {"question": "How can I create an AI song with any artist on woodcarvings?", "answer": "With woodcarvings, you can generate AI-powered songs inspired by your favorite artists in just a few steps:", "feature1": "Choose a Style – Select the genre, mood, and artist style you want your AI-generated song to resemble.", "feature2": "Enter Lyrics or Generate Automatically – Use our AI lyrics generator or input your own lyrics for a custom touch.", "feature3": "AI Song Generation – Our AI music generator will create a full track, matching the style of your selected artist.", "feature4": "Customize & Download – Adjust the melody, instruments, or vocals, then export your AI-generated song.", "conclusion": "Start making music inspired by your favorite artists with woodcarvings today!"}, "f": {"question": "Tips for Creating Better AI Songs", "answer": "Want to make high-quality AI-generated music with woodcarvings? Follow these tips:", "feature1": "Choose the Right Style – Select a genre and mood that match your vision for the song.", "feature2": "Use Strong Lyrics – Craft compelling lyrics with our AI lyrics generator or customize them for a personal touch.", "feature3": "Experiment with Melodies – Adjust tempo, instruments, and rhythm using our AI music generator to refine your track.", "feature4": "Enhance with Vocal Effects – Use our AI song generator with vocals for a professional sound or try the free vocal remover for an instrumental version.", "feature5": "Refine & Edit – Listen to your song, make tweaks, and experiment with different settings for the best result.", "conclusion": "Start creating better AI songs today with woodcarvings!"}, "g": {"question": "Is AI-generated music legal?", "answer": "Yes, AI-generated music is generally legal, but its usage depends on copyright laws and platform policies. Here's what you need to know:", "feature1": "Original AI-Generated Music – Songs created with woodcarvings are unique and free to use, but always check if you need commercial rights.", "feature2": "Copyrighted Material – If AI-generated music mimics a specific artist's voice or style, usage may be restricted due to copyright or likeness rights.", "feature3": "Fair Use & Licensing – Some AI-generated content may require proper attribution or licensing for commercial use.", "conclusion": "Always review the copyright guidelines in your region before distributing or monetizing AI-generated music."}, "h": {"question": "Can I use the generated music for commercial purposes?", "answer": "Yes, with wood carvings, you can use music for a variety of commercial applications, including social media platforms like TikTok, Instagram, YouTube, video blogs, games, and more. All music generated is 100% royalty-free, ensuring you can use it commercially without any additional fees or taxes."}, "i": {"question": "Can I create music in different languages?", "answer": "Yes, we support songs in a wide range of global languages, including French songs, English songs, Spanish songs, German songs, Russian music, and many more. With woodcarvings, you can download your tracks in high-quality formats like WAV, FLAC, and MP3."}, "j": {"question": "Is my generated music private?", "answer": "Yes, all music created using wood carvings is private and accessible only to you. By selecting \"private,\" the music remains exclusively yours, with options to download or separate it into vocals and instrumentals for further use."}, "k": {"question": "Is vocal removal free on wood carvings?", "answer": "Yes! Our vocal removal feature is completely free to use on wood carvings. No hidden fees or limits – starting separating vocals and instrumentals today!"}, "l": {"question": "Does vocal removal affect audio quality?", "answer": "No. Our AI voice remover uses advanced technology to ensure both vocal and instrumental tracks retain their original quality."}, "m": {"question": "What audio formats and lengths are supported?", "answer": "We support all major formats (MP3, WAV, etc.) and handle files up to several hours long."}, "n": {"question": "Can I remove vocals from non-music audio?", "answer": "Absolutely! Our tool works on movie soundtracks, video audio, and more, delivering high-quality vocal separation."}, "o": {"question": "How fast is the vocal removal process?", "answer": "Most files are processed in under a minute, even for long tracks."}, "p": {"question": "Is my audio safe on wood carvings?", "answer": "We don't store your files or URLs – your data is 100% secure."}, "q": {"question": "Can I preview the results before downloading?", "answer": "Yes! Preview both the vocal and instrumental tracks before downloading."}, "r": {"question": "What is an AI Caseoh Song Maker?", "answer": "An AI Caseoh Song Maker is an online tool powered by artificial intelligence that helps users generate music in the style of <PERSON><PERSON> -- a popular content creator known for his unique musical vibe. With wood carvings, you can easily create Caseoh-style songs by entering simple prompts, no music skills required. Just type, click, and listen!"}, "s": {"question": "What are the best prompts for music generator AI?", "answer": "The best prompts for music generator AI are specific and descriptive. Include genre, mood, instruments, tempo, and any specific elements you want. For example: \"Create an upbeat electronic dance track with strong bass drops, synth leads, and a gradual build-up to an energetic chorus at 128 BPM.\" Even with a simple input like \"happy pop song\" or \"sad piano ballad,\" you can get impressive results--just type your basic idea and click the AI lyrics generator button in the lower left corner to get started."}, "t": {"question": "Can I use wood carvings as an ai rap lyric generator?", "answer": "'Absolutely! Our platform includes a powerful AI lyrics generator that excels at creating rap lyrics. You can input your theme or topic, and our AI will generate unique, rhyming lyrics perfect for your next track. It's a core feature of our rap song maker."}, "u": {"question": "Is wood carvings a free musician website?", "answer": "Yes, wood carvings is proud to be a free musician website. We offer a robust free plan that provides daily credits, allowing you to generate music, create lyrics, and use our tools without any cost. It's the perfect way for any musician to start creating without a budget."}}}, "admin": {"menu": {"general": "General", "users": "Users"}, "users": {"adminRole": "Admin", "delete": "Delete", "deleteUser": {"deleted": "User has been deleted successfully!", "deleting": "Deleting user...", "notDeleted": "User could not be deleted. Please try again."}, "emailVerified": {"verified": "Email verified", "waiting": "Email waiting for verification"}, "impersonate": "Impersonate", "impersonation": {"impersonating": "Impersonating as {name}...", "unimpersonating": "Unimpersonating..."}, "loading": "Loading users...", "resendVerificationMail": {"error": "Could not resend verification mail. Please try again.", "submitting": "Resending verification mail...", "success": "Verification mail has been sent.", "title": "Resend verification mail"}, "search": "Search for name or email...", "title": "Manage users"}}, "auth": {"confirmation": {"close": "Close this window", "message": "You can close this window and continue in the window you came from.", "title": "You have been logged in!"}, "continueWithProvider": "Continue with {provider}", "forgotPassword": {"backToSignin": "Back to signin", "email": "Email", "hints": {"linkNotSent": {"message": "We are sorry, but we were unable to send you a link to reset your password. Please try again later.", "title": "<PERSON> not sent"}, "linkSent": {"message": "We have sent you a link to continue. Please check your inbox.", "title": "<PERSON> sent"}}, "message": "Please enter your email address and we will send you a link to reset your password.", "submit": "Send link", "title": "Forgot your password?"}, "invalidToken": {"message": "The link is either invalid or expired. Please try to log in again.", "title": "Invalid token"}, "login": {"createAnAccount": "Create an account", "dontHaveAnAccount": "Don't have an account yet?", "email": "Email", "forgotPassword": "Forgot password?", "hints": {"invalidCredentials": "The email or password you entered are invalid. Please try again.", "linkNotSent": {"message": "We are sorry, but we were unable to send you a magic link. Please try again later.", "title": "<PERSON> not sent"}, "linkSent": {"message": "We have sent you a link to continue. Please check your inbox.", "title": "<PERSON> sent"}}, "modes": {"magicLink": "Magic link", "password": "Password"}, "password": "Password", "sendMagicLink": "Send magic link", "submit": "Sign in", "subtitle": "Please enter your credentials to sign in.", "title": "Welcome back", "useEmail": "Sign in with <PERSON><PERSON>", "or": "Or", "autoRegister": "New users will be automatically registered", "emailRequired": "Please enter your email first", "verificationCode": "Verification Code", "enterVerificationCode": "Enter verification code", "getCode": "Get Code", "verifying": "Verifying...", "invalidEmail": "Invalid email format", "emptyVerificationCode": "Verification code cannot be empty", "sendSuccess": "<PERSON><PERSON> successfully", "sendFailed": "Send failed", "securityCode": "Verification Code", "wrongSecurityCode": "Verification Code is incorrect", "expiredSecurityCode": "Verification Code has expired", "successfulLogin": "Login successful", "accountLinkedWithGoogle": "This account is already linked to a Google account. Please log in with your Google account."}, "setup": {}, "signup": {"alreadyHaveAccount": "Already have an account?", "email": "Email", "hints": {"signupFailed": "We are sorry, but we were unable to create your account. Please try again later.", "verifyEmail": "We have sent you a link to verify your email. Please check your inbox."}, "message": "We are happy that you want to join us. Please fill in the form below to create your account.", "name": "Name", "password": "Password", "passwordHint": "Please use at least 8 characters, one uppercase and lowercase letter, one number and one special character.", "signIn": "Sign in", "submit": "Create account", "title": "Create an account"}, "teamInvitation": {"description": "You have been invited to join a team. By logging in or signing up you will automatically accept the invitation and join the team.", "title": "Accept team invitation"}, "verifyOtp": {"errors": {"otpTooShort": "The one time password is too short."}, "hints": {"verificationFailed": "We are sorry, the password you entered is invalid. Please try again."}, "message": "You have mail! Please check your inbox for the one time password we sent you. You can also click the link in the email and this page will redirect you automatically.", "otp": "One time password", "submit": "Continue", "title": "Verify your email"}, "verification": {"checkEmailForCode": "Please check your email. We have sent you a verification code."}}, "blog": "Blog", "changelog": {"description": "Stay up to date with the latest changes in our product.", "title": "Changelog"}, "common": {"confirmation": {"cancel": "Cancel", "confirm": "Confirm"}, "locales": {}, "menu": {"blog": "Blog", "changelog": "Changelog", "dashboard": "Dashboard", "logout": "Sign Out", "profile": "Profile", "docs": "Docs", "faq": "FAQ", "login": "Sign In", "pricing": "Pricing"}, "tableOfContents": {"title": "On this page"}, "points": "points", "confirmRenewalText": "Confirm Subscription Renewal", "confirmRenewal": "Would you like to renew your subscription?", "processing": "Processing...", "confirmRenewalButton": "Renew Subscription"}, "createTeam": {"name": "Team name", "notifications": {"error": "We are sorry, but we were unable to create your team. Please try again later.", "success": "Your team has been created. You can now invite members."}, "submit": "Create team", "title": "Create a team"}, "dashboard": {"menu": {"admin": "Admin", "aiDemo": "AI Demo", "dashboard": "Dashboard", "settings": "Settings"}, "sidebar": {"createTeam": "Create a new team"}, "subtitle": "See the latest stats of your awesome business.", "userMenu": {"accountSettings": "Account settings", "colorMode": "Color mode", "documentation": "Documentation", "language": "Language", "logout": "Logout", "unimpersonate": "Unimpersonate"}, "welcome": "Welcome {name}!"}, "faq": {"description": "Find answers to frequently asked questions about our AI music generator.", "title": "Frequently Asked Questions"}, "mail": {"common": {"openLinkInBrowser": "If you want to open the link in a different browser than your default one, copy and paste this link:", "otp": "One-time password", "useLink": "or use the following link:"}, "emailChange": {"body": "Hey {name},\nyou changed your email. Please click the link below to confirm your new email address.", "confirmEmail": "Confirm email", "subject": "Confirm your email change"}, "forgotPassword": {"body": "Hey {name},\nyou requested a password reset.\n\nYou can either enter the one-time password below manually in the application", "resetPassword": "Reset password", "subject": "Reset your password"}, "magicLink": {"body": "Hey {name},\nyou requested a login email from supastarter.\n\nYou can either enter the one-time password below manually in the application", "login": "<PERSON><PERSON>", "subject": "Login to supastarter"}, "newUser": {"body": "Hey {name},\nthanks for signing up for supastarter.\n\nTo start using our app, please confirm your email address by clicking the link below.", "confirmEmail": "Confirm email", "subject": "Welcome to supastarter"}, "newsletterSignup": {"body": "Thank you for signing up for the supastarter newsletter. We will keep you updated with the latest news and updates.", "subject": "Welcome to our newsletter"}, "teamInvitation": {"body": "You have been invited to join the team {teamName}. Click the button below or copy and paste the link into your browser of choice to accept the invitation and join the team.", "headline": "Join the team {teamName}", "join": "Join the team", "subject": "You have been invited to join a team"}}, "newsletter": {"email": "Email", "hints": {"success": {"message": "Thank you for subscribing to our newsletter. We will keep you posted.", "title": "Subscribed"}}, "submit": "Subscribe", "subtitle": "Be among the first to get access to supastarter.nextjs.", "title": "Get early access"}, "onboarding": {"account": {"avatar": "Avatar", "avatarDescription": "Click the circle or drop an image to it to upload your avatar.", "name": "Name", "title": "Complete your account"}, "back": "Back", "complete": "Complete", "continue": "Continue", "message": "Just a few quick steps to get you started.", "notifications": {"accountSetupFailed": "We are sorry, but we were unable to set up your account. Please try again later."}, "step": "Step {step} / {total}", "team": {"joinTeam": "Join the team", "name": "Team name", "title": "Create a team", "joinTeamDescription": "You are joining the team <strong>{teamName}</strong>."}, "title": "Set up your account"}, "pricing": {"description": "Choose the plan that works best for you.", "month": "month", "monthly": "Monthly", "subscribe": "Subscribe", "title": "Pricing", "year": "year", "yearly": "Yearly"}, "settings": {"account": {"avatar": {"description": "To change your avatar click the picture on the right side of this block and select a file from your computer to upload.", "title": "Your avatar"}, "changeEmail": {"description": "Enter a new email and click save to update it.", "note": "You will need to verify your new email address before you can sign in again.", "title": "Your email"}, "changeName": {"title": "Your name"}, "changePassword": {"description": "To change your password, enter the new one below and click save.", "note": "Please enter at least 8 characters.", "title": "Your password"}, "deleteAccount": {"confirmation": "Are you sure you want to delete your account?", "description": "Permanently delete your account. Once you delete your account, there is no going back. Please be certain.", "submit": "Delete account", "title": "Delete account"}, "title": "Account"}, "billing": {"cancelSubscription": {"notifications": {"error": {"title": "Could not cancel subscription"}, "success": {"title": "Subscription was canceled"}}}, "createCustomerPortal": {"label": "Manage billing details", "notifications": {"error": {"title": "Could not create a customer portal session. Please try again."}}}, "pauseSubscription": {"label": "Pause subscription", "notifications": {"error": {"title": "Could not pause the subscription. Please try again."}, "success": {"title": "The subscription was paused."}}}, "resumeSubscription": {"notifications": {"error": {"title": "Could not resume subscription"}, "success": {"title": "Subscription was resumed"}}}, "subscription": {"cancel": "Cancel your subscription", "currentPlan": "Your current plan", "currentSubscription": "Your current subscription", "endsOn": "Your subscription ends on <strong>{nextPaymentDate, date, medium}</strong>", "freePlan": {"title": "Free"}, "month": "month", "monthly": "Monthly", "nextPayment": "The next payment will be on <strong>{nextPaymentDate, date, medium}</strong>", "pauseSubscription": "Pause your subscription", "resume": "Resume subscription", "resumeSubscription": "Resume your subscription", "status": {"active": "Active", "canceled": "Canceled", "expired": "Expired", "incomplete": "Incomplete", "past_due": "Past due", "paused": "Paused", "trialing": "Trialing", "unpaid": "Unpaid"}, "subscribe": "Subscribe", "switchToPlan": "Switch to this plan", "updateBillingDetails": "Update billing details", "upgradePlan": "Upgrade your plan", "year": "year", "yearly": "Yearly", "yourSubscription": "Your are subscribed to <strong>{plan}</strong> ({price}/{interval})"}, "title": "Billing"}, "menu": {"account": {"general": "General", "title": "Account"}, "team": {"billing": "Billing", "general": "General", "members": "Members", "title": "Team"}}, "notifications": {"accountDeleted": "Your account has been deleted.", "accountNotDeleted": "We were unable to delete your account. Please try again later.", "avatarNotUpdated": "We were unable to update your avatar. Please try again.", "avatarUpdated": "Your avatar has been updated.", "emailUpdated": "Your email has been updated.", "nameUpdateFailed": "Could not update your name. Please try again later.", "nameUpdated": "Your name has been updated.", "passwordNotUpdated": "We were unable to update your password. Please try again.", "passwordUpdated": "Your password has been updated.", "teamDeleted": "Your team has been deleted.", "teamNameNotUpdated": "We were unable to update your team name. Please try again later.", "teamNameUpdated": "Your team name has been updated.", "teamNotDeleted": "We were unable to delete your team. Please try again later."}, "save": "Save", "sections": {"team": "Team"}, "subtitle": "Manage your settings", "team": {"changeName": {"description": "Enter a new name and click save to update it.", "teamUrl": "Your team url will be <strong>{url}</strong>.", "title": "Team name"}, "deleteTeam": {"confirmation": "Are you sure you want to delete your team?", "description": "Permanently delete your team. Once you delete your team, there is no going back. Please be certain.", "submit": "Delete team", "title": "Delete team"}, "members": {"activeMembers": "Active members", "invitations": {"email": "Email", "empty": "You have not invited any members yet.", "revoke": "Revoke invitation"}, "invite": "Invite a new member", "inviteMember": {"email": "Email", "notifications": {"error": {"description": "We were unable to invite the member. Please try again later.", "title": "Could not invite member"}, "success": {"description": "The member has been invited.", "title": "Member invited"}}, "role": "Role", "submit": "Invite", "title": "Invite new member"}, "notifications": {"removeMember": {"error": {"description": "Could not remove the member from your team. Please try again."}, "loading": {"description": "Removing member from team..."}, "success": {"description": "The member has been successfully removed from your team."}}, "revokeInvitation": {"error": {"description": "The invitation could not be revoked. Please try again later."}, "loading": {"description": "Revoking invitation..."}, "success": {"description": "The invitation has been revoked."}}, "updateMembership": {"error": {"description": "Could not update team membership. Please try again."}, "loading": {"description": "Updating membership..."}, "success": {"description": "Membership was updated successfully"}}}, "pendingInvitations": "Pending invitations", "removeMember": "Remove member", "roles": {"member": "Member", "owner": "Owner"}, "title": "Members"}, "subtitle": "Manage your team settings and members", "title": "Team"}, "title": "Settings"}, "zod": {"errors": {"custom": {"email_already_exists": "This email is already in use.", "email_not_verified": "Please verify your email address first.", "lowercase_character_required": "Please use at least one lowercase character.", "number_required": "Please use at least one number.", "special_character_required": "Please use at least one special character ({character}).", "uppercase_character_required": "Please use at least one uppercase character."}, "invalid_arguments": "Invalid function arguments", "invalid_date": "Invalid date", "invalid_enum_value": "Invalid enum value. Expected {- options}, received '{received}'", "invalid_intersection_types": "Intersection results could not be merged", "invalid_literal": "Invalid literal value, expected {expected}", "invalid_return_type": "Invalid function return type", "invalid_string": {"cuid": "Invalid {validation}", "datetime": "Invalid {validation}", "email": "Invalid {validation}", "endsWith": "Invalid input: must end with \"{endsWith}\"", "regex": "Invalid", "startsWith": "Invalid input: must start with \"{startsWith}\"", "url": "Invalid {validation}", "uuid": "Invalid {validation}"}, "invalid_type": "Expected {expected}, received {received}", "invalid_type_received_null": "Required", "invalid_type_received_undefined": "Required", "invalid_union": "Invalid input", "invalid_union_discriminator": "Invalid discriminator value. Expected {- options}", "not_finite": "Number must be finite", "not_multiple_of": "Number must be a multiple of {multipleOf}", "too_big": {"array": {"exact": "Array must contain exactly {maximum} element(s)", "inclusive": "Array must contain at most {maximum} element(s)", "not_inclusive": "Array must contain less than {maximum} element(s)"}, "date": {"exact": "Date must be exactly {- maximum, datetime}", "inclusive": "Date must be smaller than or equal to {- maximum, datetime}", "not_inclusive": "Date must be smaller than {- maximum, datetime}"}, "number": {"exact": "Number must be exactly {maximum}", "inclusive": "Number must be less than or equal to {maximum}", "not_inclusive": "Number must be less than {maximum}"}, "set": {"exact": "Invalid input", "inclusive": "Invalid input", "not_inclusive": "Invalid input"}, "string": {"exact": "String must contain exactly {maximum} character(s)", "inclusive": "String must contain at most {maximum} character(s)", "not_inclusive": "String must contain under {maximum} character(s)"}}, "too_small": {"array": {"exact": "Array must contain exactly {minimum} element(s)", "inclusive": "Array must contain at least {minimum} element(s)", "not_inclusive": "Array must contain more than {minimum} element(s)"}, "date": {"exact": "Date must be exactly {- minimum, datetime}", "inclusive": "Date must be greater than or equal to {- minimum, datetime}", "not_inclusive": "Date must be greater than {- minimum, datetime}"}, "number": {"exact": "Number must be exactly {minimum}", "inclusive": "Number must be greater than or equal to {minimum}", "not_inclusive": "Number must be greater than {minimum}"}, "set": {"exact": "Invalid input", "inclusive": "Invalid input", "not_inclusive": "Invalid input"}, "string": {"exact": "String must contain exactly {minimum} character(s)", "inclusive": "String must contain at least {minimum} character(s)", "not_inclusive": "String must contain over {minimum} character(s)"}}, "unrecognized_keys": "Unrecognized key(s) in object: {- keys}"}, "types": {"array": "array", "bigint": "bigint", "boolean": "boolean", "date": "date", "float": "float", "function": "function", "integer": "integer", "map": "map", "nan": "nan", "never": "never", "null": "null", "number": "number", "object": "object", "promise": "promise", "set": "set", "string": "string", "symbol": "symbol", "undefined": "undefined", "unknown": "unknown", "void": "void"}, "validations": {"cuid": "cuid", "cuid2": "cuid2", "datetime": "datetime", "email": "email", "emoji": "emoji", "ip": "up", "regex": "regex", "ulid": "ulid", "url": "url", "uuid": "uuid"}}, "textToMusic": "Text/Lyric to Music", "royaltyFree100": "100% Royalty-free", "supportIsolateVocals": "AI Lyrics Generator", "whyChooseOurAIMusicGen": "Why Choose Our AI Music Generator?", "useMusicWithoutLicenseWorry": "No need for complex software—our AI music generator from text lets you create high-quality songs with just a few clicks.", "multipleLanguages": "Multilingual Support – Create Music in Any Language", "createMultiLangMusic": "Our AI song generator supports multiple languages, allowing users worldwide to create music in their native tongue.", "roundTheClockSupport": "Random Generation – Endless Creativity", "get247Help": "Struggling with inspiration? The random song generator provides unique song structures and melodies, giving you fresh ideas every time.", "createSong": "Create Song", "vocalRemover": "Vocal Remover – Get Clean Instrumentals", "customLyrics": "Lyric Generate", "descriptionMode": "Description Mode", "instrumentalOnly": "Instrumental Only", "enterYourLyrics": "Enter your lyrics here...", "describeDesiredSong": "Describe the kind of song you want...", "generatedLyrics": "Generated lyrics...", "generating": "Generating", "changeOne": "change", "aiGenerate": "AI generate", "styles": "Styles", "random": "Random", "enterStyle": "Enter style...", "add": "Add", "showLess": "Show Less", "showMore": "Show More", "title": "Title", "enterSongTitle": "Enter your song title", "generateMusic": "AI Music Generate", "tips": "Tips", "freeTrialUsedUp": "Free trial uses exhausted. Log in to receive points and continue generating music for free.", "generateHighQualityMusic": "Generate high-quality music in seconds", "anyStyleMusic": "Any Style of Music, From Pop to Classical", "musicGenerator": "music generator", "instrumentalSongMaker": "Instrumental song maker", "aiRapSongMaker": "AI rap song maker", "playVolume": "plays", "likeVolume": "likes", "makeSongNow": "makesong Now", "makeAiSongsAnyLang": "Make AI Songs in Any Language", "englishSong": "English Song", "germanSongs": "German Songs", "brazilianSong": "Brazilian Song", "russianSong": "Russian Song", "andMore": "and more", "makeUnlimitedSongWithAI": "How to Convert Lyrics or Text to Song for Free with AI?", "songDescriptionMode": "Lyric Generate - Lyrics to Song", "descriptionToSong": "Have your song lyrics ready? Input them directly, our AI music generator supports directly creating music. Or using our AI lyrics generator to transform them into a unique version. Whether you need rap lyrics, suits theme song lyrics, or just want to experiment, our tool makes it easy to generate royalty-free music.", "customerMode": "Description Mode - Text to Song", "jmakesongLyricsInput": "Got a song idea? Describe it in a few words, and our AI music generator will transform it into a full track. No need to overthink – just share your vision, and let our smart technology handle the rest. Whether you're dreaming of a catchy songs or a heartfelt ballad, we make it easy to turn your words into a song online for free!", "freeAISongGen": "Free AI song generator", "newToolForSongMakers": "A new tool for Song Makers", "aiPoweredMusicTools": "Create, split, and transform your music with our AI-powered tools", "vocalRemoverAndSplitter": "Vocal Remover & Instrumental AI Splitter", "splitAfterGen": "Once the song is generated, click the Splitter button. You will get two tracks: a karaoke version and an acapella version.", "importAndSeparate": "Import your own music files and let AI separate the vocals from the instrumental.", "tryVocalRemover": "Try AI Vocal Remover", "instrumentalGen": "Instrumental Music Generator", "createInstrumental": "Create Instrumental Music", "moodMatchInst": "Create instrumental music that matches your mood, without vocals or lyrics.", "simpleModeInst": "Simple Mode – Enter a description and let AI create the perfect match.", "customModeInst": "Custom Mode – Select your preferred style and get a tailored piece.", "genAILyricsOneClick": "Generate AI Lyrics In One Click", "shareForLyrics": "Share your theme or topic, and our AI will create unique, rhyming lyrics that match your vision. Transform your ideas into powerful words.", "generateNow": "Generate Lyrics Now", "listenDemo": "Listen to our demo:", "original": "Original", "vocals": "Vocals", "instrumentals": "Instrumentals", "aiSongGenAnyMoment": "AI Music Generator: Revolutionizing Music Creation Across Industries", "makeSongsForMedia": "Make music creation easy for everyone—creators, educators, and artists alike!", "musicMakerNow": "Music Maker Now", "makeSongForSocialCreators": "Content Creation & Entertainment", "unleashMusicPossibilities": "AI song maker provides royalty-free background music for videos, games, and apps, helping creators avoid licensing issues.", "createBgMusic": "Generate custom tracks from text prompts, perfect for YouTubers, filmmakers, and game developers.", "makeSongForIndieMusicians": "Advertising & Podcast Music", "trueRoyaltyFree": "AI-generated advertising music helps brands create catchy, memorable jingles and background scores for commercials.", "generateProQualityTracks": "AI music generator with vocals allows advertisers to craft professional-quality music tailored to different campaigns.", "generateGameMusic": "Personalized Music & Playlists", "makeSongGameStyles": "AI song maker craft personalized playlists based on user preferences, ensuring the perfect soundtrack for any mood or activity.", "gameMusicEnhanceExp": "Text-to-song tool transform written lyrics into full tracks, making music composition easier for everyone.", "freeDownloadGameAudio": "AI music beat maker online (free, no download required) generates instant beats for rap, country, and pop music.", "getPodcastMusic": "Education & Music Composition", "solveCopyrightIssues": "AI melody maker helps learners understand chords, harmony, and rhythm, making music theory more interactive.", "podcastMusicGen": "AI-powered lyrics generators, including rap lyrics generators, assist songwriters in creating professional-quality lyrics.", "musicForAdvertising": "AI-Powered Song Customization & Sound Editing", "makeSongForBrands": "Create songs with your voice using AI-generated instrumentals and vocals for studio-quality tracks.", "generateBrandEarworms": "AI vocal remover (free) allows for remixing, karaoke, and instrumental creation.", "freeOnlineMakeSong": "Free makesong Online", "inspiringMusicStories": "Hear from Our Users About woodcarvings", "makeMusicalDreamsTrue": "With MakeSong,\n Make your musical dreams come true with ease!", "songMakerAI": "Song Maker AI", "termsAndConditions": "Terms and conditions", "privacyPolicy": "Privacy policy", "noMusicSelected": "Pick or generate a song to see details, lyrics, and style.", "noTags": "No tags", "audio": "Audio", "video": "Video", "inspiration": "inspiration", "defaultStyle": "Default style", "sonicMaker": "Music generator", "vocalIsolation": "Vocal Isolation", "upgradeNow": "Upgrade now", "splitterAI": "Splitter AI", "splitMusicDesc": "Use our AI vocal remover to isolate vocals, instruments, and beats effortlessly", "uploadPrompt": "Drop your audio file here or click to upload", "supportedFormats": "Supports MP3, WAV (max 10MB)", "welcomeBack": "Welcome Back!", "signOut": "Sign Out", "confirmSignOut": "Are you sure you want to sign out?", "confirmSignOutText": "Confirm Sign Out", "soprano": "<PERSON><PERSON><PERSON>", "alto": "Alto", "tenor": "<PERSON><PERSON>", "bass": "Bass", "childrensVoice": "Children's Voice", "maleVoice": "Male Voice", "femaleVoice": "Female Voice", "style": "Style", "moods": "Moods", "voices": "Voices", "tempos": "Tempos", "freeTrail": {"unlockUnlimitedGeneration": "Unlock Unlimited Generation", "freeTrialUsed": "You've used your free trial. Login or create an account to continue using our AI music generation service!", "loginNow": "Login Now", "createAccount": "Create Account", "memberBenefits": "Member Benefits", "unlimitedGeneration": "Unlimited Music Generation", "highQualityVoice": "Premium Voice Options", "batchExport": "Batch Export", "oneTimeFreeTrialAvailable": "One-time Free Trial Available"}, "tipwith50": "Please enter lyrics to create your music (recommended length: 50-200 characters for best results)", "makeSongTagline": "AI Music & Song Generator Online – Free | wood carvings", "makeSongDesc": "AiMakeSong: Your free ai song generator for royalty-free music. Turn text to music or bring lyrics to song. Includes vocal remover, ai stem splitter & a rap lyrics generator.", "descriptionPrompt": "Please enter Description to create your music (recommended length: 50 - 200 characters for best results)", "unlockDownload": "Unlock download feature", "loginPrompt": "Please click the button below to login and continue", "price": {"title": "Affordable Plans for Every Music Creator", "subtitle": "Try AI Music Generator on woodcarvings for free, then pick a plan that fits your music-making journey!", "save10": "Save {discount10}%", "save20": "Save {discount20}%", "save60": "Save {discount60}%", "popularBadge": "Popular", "subscribeButton": "Subscribe Now", "tryNowButton": "Try Now", "freePlan": {"title": "Free", "price": "Free", "credits": "{freeCredits} Free Credits / Day (Reset every 24 hours)", "features": {"feature1": "{freeCredits} Free Credits / Day", "feature2": "{cloudStorageDays}-day cloud storage", "feature3": "Shared generation queue"}, "disabledFeatures": {"feature1": "Commercial License & Unrestricted Usage Rights", "feature2": "Private Generations", "feature3": "High-Quality Lossless Download"}}, "basicPlan": {"title": "Basic", "price": "${monthlyPriceBasic} / Month", "credits": "{creditsBasic} Credits / Month", "features": {"feature1": "{creditsBasic} Credits / Month", "feature2": "High-Quality Lossless Download", "feature3": "Commercial License & Unrestricted Usage Rights", "feature4": "Dedicated Support Representative", "feature5": "{concurrentJobsBasic} running jobs at once", "feature6": "Priority generation queue", "feature7": "Private Generations", "feature8": "{cloudStorageDaysBasic}-day cloud storage", "yearlyFeature1": "{creditsBasicYearly} credits per year"}, "yearlyPrice": "${yearlyPriceBasic}/year", "yearlyCredits": "{creditsBasicYearly} credits per year", "yearlyPricePerMonth": "${yearlyMonthlyBasic}/mo"}, "advancedPlan": {"title": "Advanced", "price": "${monthlyPriceAdvanced} / Month", "credits": "{creditsAdvanced} Credits / Month", "features": {"feature1": "{creditsAdvanced} Credits / Month", "feature2": "High-Quality Lossless Download", "feature3": "Commercial License & Unrestricted Usage Rights", "feature4": "Dedicated Support Representative", "feature5": "{concurrentJobsAdvanced} running jobs at once", "feature6": "Priority generation queue", "feature7": "Private Generations", "feature8": "{cloudStorageDaysAdvanced}-day cloud storage", "yearlyFeature1": "{creditsAdvancedYearly} credits per year"}, "yearlyPrice": "${yearlyPriceAdvanced}/year", "yearlyCredits": "{creditsAdvancedYearly} credits per year", "yearlyPricePerMonth": "${yearlyMonthlyAdvanced}/mo"}, "unlimitedPlan": {"title": "Unlimited", "price": "${monthlyPriceUnlimited} / Month", "credits": "Unlimited Credits / Month", "features": {"feature1": "Unlimited Credits / Month", "feature2": "High-Quality Lossless Download", "feature3": "Commercial License & Unrestricted Usage Rights", "feature4": "Dedicated Support Representative", "feature5": "{concurrentJobsUnlimited} running jobs at once", "feature6": "Priority generation queue", "feature7": "Private Generations", "feature8": "Unlimited cloud storage"}, "yearlyPrice": "${yearlyPriceUnlimited}/year", "yearlyPricePerMonth": "${yearlyMonthlyUnlimited}/mo"}, "monthlyBilling": "Monthly", "yearlyBilling": "Yearly", "saveBig": "Save", "saveYearly": "Save {discount}%", "yearlyBillingNote": "Billed annually", "monthlyBillingNote": "Billed monthly, cancel anytime", "yearSavings": "Save ${yearSavings}/year"}, "pricingFaq": {"title": "wood carvings Pricing FAQ", "subtitle": "Frequently asked questions and answers about wood carvings pricing", "payment": {"question": "Is it safe to make payments on woodcarvings?", "answer": "You can make payments on woodcarvings with complete peace of mind. The platform uses our trusted payment system, which offers bank-level security. Additionally, woodcarvings cannot access any of your payment information, as all payment data is directly encrypted and processed by the payment platform."}, "credits": {"question": "How many Credits does it cost to generate songs?", "answer": "Each generation consumes 3 Credits and creates two songs for you. In other words, each song costs an average of 1.5 Credits."}, "subscription": {"question": "How does woodcarvings's subscription pricing work?", "answer": "We offer three subscription tiers: Pro, Advanced, and Ultra, each available in monthly or annual billing options. Monthly subscriptions auto-renew every month, while annual subscriptions auto-renew yearly. The first billing date is the day you purchase your subscription."}, "runningJobs": {"question": "What are Running jobs?", "answer": "Running jobs represent the number of simultaneous music generation tasks you can perform. 1 running job means you can generate one set of songs (2 songs) at a time, waiting for completion before starting the next set. 2 running jobs allow you to generate two sets simultaneously (4 songs total), and so on."}, "license": {"question": "What does the Commercial License include?", "answer": "Free users cannot download their generated songs and don't own the rights to their works. Subscribers can download all generated songs (including those created as a free user) and receive full commercial license rights, allowing them to freely use their works, including uploading to various music platforms."}, "freeGen": {"question": "How many free generations does woodcarvings offer per day?", "answer": "Within each 24-hour period, you can use 1-2 free generation opportunities, creating 2-4 songs. Note: The timing is based on your usage time rather than a fixed timezone. For example, if you use up your free credits at 10 AM today, you'll need to wait until 10 AM tomorrow to use them again."}, "storage": {"question": "How long does woodcarvings store generated songs?", "answer": "Free users: Songs are automatically deleted after 1 day; Pro subscribers: Songs are stored for several months; Advanced subscribers: Songs are stored for 1 year; Ultra subscribers: Songs are stored permanently with no deletion concerns."}, "private": {"question": "What's the difference between Public and Private generations?", "answer": "Public Generation means other users can enjoy your created works. Private Generation ensures your works are only visible to you, and other users cannot access them."}, "support": {"text": "Still have questions? Email us at"}}, "pricingBottomArea": {"title": "Unleash Your Musical Genius with wood carvings", "description": "Experience the future of music creation. Our AI Song Generator turns your ideas into unique melodies, no musical expertise required. Join now and compose your next hit in minutes.", "createButton": "Create Your Song"}, "contact": {"text": "Contact", "title": "Contact us", "description": "We are always looking for ways to improve our products and services. Contact us and let us know how we can help you.", "email": {"label": "Email"}}, "insufficientCredits": "Insufficient credits", "hoverToPreview": "Hover to preview the sample", "moreFreeCredits": "More Free Credits", "getAdditionalCredits": "Get additional credits for generating music", "downloadMusic": "Download Music", "saveAndDownload": "Save and download your generated tracks", "permanentStorage": "Permanent Storage", "safeStorage": "Your music is safely stored forever", "downloading": "Downloading", "loginForFreeTrial": "Log in for a free trial", "tipLogin": "Your creative work deserves to be preserved completely! After logging in, the system will automatically store all your generated music works, ensuring no creativity is lost", "loginTipsTitle": "<PERSON><PERSON>", "freeChannelQueuing": "Free Channel Queuing...", "highTrafficWaitMessage": "High traffic, estimated waiting time", "closeWindowWarning": "Please do not close the window, or the timer will restart", "memberBenefitsTitle": "Member Benefits:", "memberFeatures1": "Independent High-Speed Generation Channel", "memberFeatures2": "20-Second Quick Results", "memberFeatures3": "Independent High-Speed Channel", "memberFeatures4": "No Queuing Required", "creatorChoiceTitle": "More High-Quality Music Models", "globalCoverageMessage": "Works featured on major global social platforms", "socialPlatformstext1": "TikTok 100k+", "socialPlatformstext2": "RED 80k+", "socialPlatformstext3": "YouTube 50k+", "socialPlatformstext4": "Instagram 30k+", "subscribeCTA": "Subscribe Now for Priority Access", "queueingTitle": "Queuing in Progress", "cancelGeneration": "Cancel Generation", "estimatedWait": "Estimated Wait", "memberFastTrackTip": "Subscribe for exclusive fast track access with no waiting", "creatorsChoice": "The Choice of 500,000+ Creators", "notSubscriber": "You are not a subscriber and cannot perform the download operation. Subscribe to any plan to enjoy this feature immediately.", "mySongs": "My Songs", "share": {"shareAiMusic": "Share your AI music creations with one click", "letWorldHear": "Let the world hear your musical talent", "musicShareFeature": "Our new music sharing feature allows you to easily showcase your AI creations and gain appreciation from global listeners.", "share": "Share", "shareFeaturePrompt": "Share Feature Guide", "generateMusicFirst": "You need to generate music first,", "then": "then", "inMyMusicPage": "in the \"My Music\" page", "findAndClickShare": "find the piece you want to share and click the share button.", "pleaseNote": "Please note:", "subscribersOnly": "Share feature is only available to subscribers. Upgrade to Pro version to unlock this feature and enjoy more privileges!", "understood": "Got it", "upgradeSubscription": "Upgrade Subscription", "firstStep": "You need to generate music first,", "secondStep": "then in the My Music", "thirdStep": "page find the piece you want to share and click the share button."}, "linkCopied": "Link copied", "copyFailed": "Co<PERSON> failed", "magicTitle": "Magic", "line1": "AI analyzes lyrics to suggest the perfect title", "line2": "Powered by <PERSON>, <PERSON><PERSON><PERSON>'s cutting-edge AI model", "line3": "✨ Unlimited access for subscribers", "magicSubtitle": "Powered by <PERSON>, <PERSON><PERSON><PERSON>'s cutting-edge AI model", "magicFeature1": "Analyze trending search keywords from massive data", "magicFeature2": "Increase song exposure and search rates", "magicFeature3": "Help your work get discovered easier", "magicPricing": "Subscribers: 5s/time | Regular users: 2min/time", "magicSubscribe": "Subscribe Now", "headerTitle": "AI Smart Lyrics Analysis, Recommending the Best Song Title", "generatedTitle": "A song title has been written for you", "describeDesiredSong2": "Describe the music style, mood or scene you want, e.g.: 'A warm folk song, like walking in an autumn town, with maple leaves gently falling, reminiscent of long-lost friends'", "aiGenerate2": "AI Lyrics Generator", "aiLyricsDesc": "Powered by <PERSON>, this is an intelligent assistant that helps you quickly create lyrics. Whether you're a professional musician or amateur creator, it helps you break through creative blocks and spark inspiration.", "aiDescIntro": "Simply describe your ideas, and AI will help create a professional music description. Make your work more discoverable and attract more listeners.", "previewTip": "Subscribe to Preview This Style", "enterYourLyrics1": "Got lyric ideas? Write them down! Our AI will help you create your own original song.", "makeSongKeywords": "AI music generator, AI song generator, text to music, lyrics to song, create music online, free music generator, AI-generated songs", "createPageTitle": "Create AI Songs | Text to Music & Lyrics to Song - wood carvings", "createPageDesc": "Generate songs from text or lyrics with wood carvings. Select styles, moods, and voices to craft unique AI music in seconds.", "createPageKeywords": "AI song creation, text to song, lyrics to music, AI music creation, generate songs online, custom music generator", "pricingPageTitle": "wood carvings Pricing | Best AI Music Generator Plans", "pricingPageDesc": "Explore affordable plans for AI music generation. Choose the best pricing for your needs and start making music today.", "pricingPageKeywords": "AI music generator pricing, song creation plans, subscription plans, make music online pricing", "contactPageTitle": "Contact wood carvings | AI Music Generator Support", "contactPageDesc": "Need help? Contact wood carvings for support on AI music generation, pricing, and technical assistance.", "contactPageKeywords": "contact wood carvings, AI music generator support, customer service, help center", "splitterPageTitle": "AI Vocal Remover | Remove Vocals & Create Instrumentals", "splitterPageDesc": "Easily remove vocals from any song with wood carvings's AI vocal remover. Get clean instrumentals for remixing or karaoke.", "splitterPageKeywords": "AI vocal remover, instrumental splitter, remove vocals from songs, karaoke maker, create instrumentals", "loginPageTitle": "Login to wood carvings | Access Your AI Music Account", "loginPageDesc": "Log in to wood carvings to create, save, and download AI-generated songs. Access your personal AI music library anytime.", "loginPageKeywords": "wood carvings login, AI music account, access AI songs, AI song generator login, user dashboard", "deleteSuccess": "Successfully deleted", "deleteFailed": "Failed to delete", "confirmDeleteMusic": "Are you sure you want to delete this music? This action cannot be undone.", "noMusic": "No music yet. Create a song with our AI song generator.", "generateFailed": "Generation Failed", "failed": "Failed", "lyricsViolation": "The lyrics violate AI content moderation policies. Please modify the lyrics to continue creation.", "songIn30s": "30s to Get Your Song", "stemSeparator": "<PERSON><PERSON> Separator", "enterSongStyle": "Enter song styles or pick below. Try our AI random song generator for instant ideas!", "contentWarning": "Lyrics, titles, and styles must not contain names of famous people or inappropriate words. Generating once costs 3 credits", "howToUse": "How Do I Make a Song with AI Using woodcarvings?", "aiDescription": "Create stunning music tracks effortlessly with our AI-powered tools. Whether you're a beginner or a pro, our platform makes music creation simple and fun!", "createMusicBtn": "Create Music with AI", "chooseInput": "Choose Your Input Type", "selectGenerate": "Start by selecting how you want to generate your song. You can choose between:", "text2music": "Text to Music: Turn your ideas into music by describing your vision in text.", "lyricsToMusic": "Lyrics to Music: Use our AI lyrics generator to create songs from your lyrics or generate new ones.", "selectVoiceStyle": "Select Voice and Style", "customizeDesc": "Customize your song by choosing:", "voiceOptions": "Voice: Male, female, or instrumental (e.g., piano, guitar)", "styleOptions": "Style: Pick from genres like pop, rock, rap, classical, or create theme music", "generateDownload": "Generate and Download Your Song", "finalStepDesc": "Once you've made your selections, click the 'Generate' button. Our AI music generator will create a unique track for you. Listen, enjoy, and download your custom song!", "communityTitle": "AI Song Generator Community | Explore AI-Made Music", "exploreDesc": "Listen to Unique Tracks Created with woodcarvings", "musicGenreTags": {"aiCountrySongGenerator": "ai country song generator", "aiRapGenerator": "ai rap generator", "rockAi": "rock ai", "instrumentalSongMaker": "Instrumental song maker"}, "intro": {"mainTitle": "Turn Your Ideas into Music with AI – Fast, Easy, and Fun!", "description": "wood carvings is your ultimate AI song generator and music maker, designed to help anyone create professional-quality AI music free in minutes. Input a brief song idea, adjust lyrics, choose a title, and pick a style – our AI music generator tool will create a full, original track for you in seconds.\nWhether you're a beginner or a pro, our platform makes music creation simple, intuitive, and enjoyable. Perfect for rap lyrics, theme music for suits lyrics, or any genre you desire!"}, "uniqueness": {"title": "What Makes wood carvings Unique?", "content": "wood carvings is a powerful AI music generator that uses advanced technology to transform your ideas into complete songs. Whether you're looking to create a song with my voice, generate rap lyrics, or make theme music for suits lyrics, our tools have you covered."}, "keyFeatures": {"title": "Key Features", "item1": "AI Lyrics Generator: Turn your thoughts into lyrics with our smart lyric creator.", "item2": "Vocal Remover Free: Isolate or remove vocals from any track effortlessly.", "item3": "AI Music Generator with Vocals: Add professional vocals to your tracks or integrate your own voice."}, "howToMakeDesc": "With wood carvings, generate high-quality music for free. Sign up to access our powerful AI song generator and music maker tools. Create unlimited music anytime, anywhere!", "howToMakeDesc1": "We offer two easy ways to make music: Lyrics-to-Song and Text-to-Song. Perfect for beginners and pros alike – start creating today!", "randomSoundMaker": "Random song generator and melody maker ensure fresh, unique sounds for creative projects.", "podcastMusicMaker": "Podcast music creation is simplified with AI, offering intro/outro themes, background ambiance, and smooth transitions for a polished listening experience.", "usersCanEasily": "Users can easily make their own ringtones or remaster tracks using AI tools.", "themedSongCreate": "Themed Song Creation & Niche Music Needs", "aiGenThemeMusic": "AI can generate theme music, such as the suits theme song lyrics, or create AI country songs for specific genres.", "rapSongMaker": "Rap song maker and melody generator assist in crafting hip-hop, pop, and country music.", "aiRemixThemes": "AI can even remix existing themes, like \"Theme music for Suits lyrics,\" into different styles.", "aiMusicIntro": "Our AI music generator is designed to make music creation easy, fast, and accessible. Whether you need an AI song generator, random song maker, lyrics generator, or vocal remover, our platform provides a seamless experience for musicians, content creators, and hobbyists.", "randomSongGenerator": "Random song generator helps you discover new melodies effortlessly, saving hours of manual composition.", "aiLyricsGenerator": "AI lyrics generator and lyrics builder help you craft the perfect words for your song in seconds.", "whetherYouAreMaking": "Whether you're making pop, rap, or country, our text-to-song feature adapts to different languages and musical styles.", "musicMakerTools": "Generate beats, lyrics, and compositions instantly with our AI music generator and music maker tools.", "aiLyricsGen": {"title": "Your Personal AI Lyrics Generator", "subtitle": "Unlock your creativity with our powerful song writing AI. This intuitive ai lyrics generator helps you craft perfect lyrics in seconds, turning your ideas into songs.", "tags": {"aiLyricsGeneratorFree": "AI Lyrics Generator Free", "rapLyricsGenerator": "Rap Lyrics Generator", "aiSongLyricsGenerator": "AI Song Lyrics Generator"}, "form": {"theme": {"label": "Theme", "placeholder": "Describe the theme of the lyrics you want to generate"}, "keywords": {"label": "Keywords", "placeholder": "Describe the keywords of the lyrics you want to generate"}, "genre": {"label": "Genre"}, "emotion": {"label": "Emotion"}, "language": {"label": "Language"}, "structure": {"label": "Structure"}, "duration": {"label": "Duration", "descriptions": {"veryShort": "Very short: 1 verse + 1 chorus", "short": "Short: 2 verses + 2 choruses", "medium": "Medium: 2-3 verses + bridge", "long": "Long: 3-4 verses + bridges", "veryLong": "Very long: 4-5 verses + multiple bridges"}}}, "songDescription": {"title": "Song Description", "placeholder": "Describe your song idea, theme, mood, or story..."}, "analyzeSong": {"title": "Analyze Existing Song (Optional)", "songNamePlaceholder": "Song name", "analyzeButton": "Analyze", "analyzingButton": "Analyzing...", "loadingText": "Analyzing song styles and elements..."}, "musicStyle": {"title": "Music Style", "showAll": "Show All", "collapse": "Collapse"}, "eraRegion": {"title": "Era & Region", "eraLabel": "Era", "regionLabel": "Region", "eras": {"70s": "1970s", "80s": "1980s", "90s": "1990s", "Modern": "Modern"}}, "musicAttributes": {"title": "Music Attributes", "tempoLabel": "Tempo", "durationLabel": "Duration", "tempo": {"Slow": "Slow", "Medium": "Medium", "Fast": "Fast", "VeryFast": "Very Fast", "Largo/Lento": "Very slow and solemn", "Andante": "Walking pace", "Moderato": "Moderate speed", "Allegro": "Fast and lively", "Presto": "Very fast", "Prestissimo": "Extremely fast"}, "duration": {"1min": "1 min", "2min": "2 min", "3min": "3 min"}}, "generateButton": {"text": "Generate Lyrics", "generating": "Generating..."}, "generatedLyrics": {"title": "Generated Lyrics", "copyButton": "Copy", "copiedButton": "Copied!", "generateMusicButton": "Generate Music"}, "errors": {"analyzeFailed": "Failed to analyze song", "noLyricsGenerated": "No lyrics generated.", "copyFailed": "Failed to copy lyrics:"}, "selectRegion": "Select Region", "search": "Search", "standout": {"title": "Why Our AI Lyrics Generator Stands Out", "description": "Discover the benefits of our advanced song writing platform. This isn't just another lyric rewriter; it's a complete tool designed for artists who demand quality, privacy, and efficiency."}, "creative": {"title": "Go Beyond Templates with Our Creative AI Lyrics Generator", "description": "Our system analyzes millions of data points to understand structure, rhyme, and flow. This unique song writing ai provides contextually aware suggestions that match your genre and mood, ensuring your lyrics are truly original and inspiring."}, "privacy": {"title": "The Best AI Tool for Song Lyrics and Privacy", "description": "Experience seamless song writing with features like free, private project storage. Unlike other tools, your ideas are yours alone. Our ai rap lyrics generator module is also fine-tuned for complex rhyme schemes, giving you a professional edge."}, "poweredBy": "Powered by Advanced AI Technology", "selectOption": "Select an option", "applications": {"title": "Real-World Song Writing Applications", "description": "See how our ai lyrics generator is used by thousands. From hobbyists to pros, our lyrics to song ai helps create compelling narratives and catchy hooks for any project with incredible efficiency.", "cta": "Break Your Block Now"}, "writersBlock": {"title": "Overcoming Writer's Block with a Powerful Lyric Rewriter", "description": "A user reported a 75% reduction in time spent on a single verse. This powerful song writing tool acts as a creative partner, using our unique lyrics generator ai to suggest new angles and rhymes.", "cta": "Break Your Block Now"}, "rapLyrics": {"title": "Crafting Authentic Rhymes with Our Rap Lyrics Generator AI", "description": "With over 50,000 rap verses generated, our ai rap lyrics generator understands modern cadence and slang. It's the best ai tool for song lyrics when you need that perfect punchline to make your track stand out.", "cta": "Generate Rap <PERSON>s"}, "loveSong": {"title": "The Perfect Assistant for Writing a Love Song", "description": "Our AI helps you find the right words to express emotion. This ai lyrics generator provides romantic vocabulary and metaphors, making the difficult process of song writing about love feel simple and heartfelt.", "cta": "Write a Love Song"}, "structure": {"title": "Structuring I<PERSON><PERSON> with a <PERSON><PERSON><PERSON> for Writing a Song", "description": "Our song writing platform provides structured outputs (verse-chorus-bridge). This lyrics to song ai helps you organize your thoughts, turning a scattered idea into a complete song with a professional and efficient workflow.", "cta": "Start Structuring"}, "imageDescriptions": {"musicianCreative": "A musician was lost in thought as he looked at his guitar, while on the laptop screen beside him, our tool was generating lyrics, solving his creative problem.", "rapperPerforming": "A rapper was performing passionately on the stage under the neon lights.", "loveSongCouple": "A pair of soft-focus, romantic style photos of a couple create an atmosphere reminiscent of a love song.", "inspiredWriter": "A person is sitting at the desk, surrounded by notes. The laptop screen displays our tools and his expression is full of inspiration."}, "quickStart": {"title": "Quick Start Guide", "subtitle": "Start Your Song Writing Journey in Two Steps", "description": "Our ai lyrics generator is designed for ultimate simplicity and efficiency. Follow this guide and use our song writing ai to create your first song in under a minute, with your privacy always protected.", "step1": {"title": "Enter Your Topic and Style", "description": "Simply input your song's theme, desired mood, and genre. Our lyrics generator ai uses this information to create a relevant and customized lyrical foundation for your project."}, "step2": {"title": "Generate, Refine, and Export", "description": "Click 'Generate' to see the magic. Our ai lyrics generator will produce verses and choruses. Use the built-in lyric rewriter to tweak lines until they're perfect, then save your work securely."}, "cta": "Try It Now, It's Free"}, "testimonials": {"title": "What Our Users Are Saying", "description": "Join thousands of musicians who have transformed their creative process with our AI-powered lyrics generator.", "testimonial1": {"quote": "This ai lyrics generator is a game-changer. It helped me finish three songs in one evening. The quality is just incredible, and it's so easy to use!", "name": "<PERSON>", "title": "Singer-Songwriter"}, "testimonial2": {"quote": "As a beginner, the template for writing a song was invaluable. It gave me the structure I needed to finally complete my first track. Highly recommend!", "name": "<PERSON>", "title": "Music Student"}, "testimonial3": {"quote": "I was stuck on a chorus for weeks. The lyric rewriter gave me ten different options in seconds. This is the best ai tool for song lyrics, period.", "name": "<PERSON>", "title": "Producer"}, "testimonial4": {"quote": "The ai rap lyrics generator is surprisingly good. The rhymes are clever and the flow feels natural. It's an essential tool for my creative process now.", "name": "<PERSON>", "title": "<PERSON><PERSON>"}, "cta": "Join Our Community"}}, "lyricsBuilder": "Our lyrics builder creates original lyrics in various genres, including pop, rap, and country.", "aiLyricsHelp": "AI lyrics generator helps refine and adjust lyrics to match the beat and melody, making songwriting effortless.", "extractVocals": "Easily extract vocals with our free vocal remover, turning any song into an instrumental version.", "createKaraoke": "Create karaoke tracks, remix songs, or isolate vocals for new music production.", "aiPowered": "Our AI-powered vocal remover ensures high-quality audio separation with minimal loss.", "whyusInstrumentalOnly": "Instrumental Only – Perfect for Karaoke & Remixing", "coverSongs": "Want to add your own vocals to popular songs? Our instrumental only tool makes it simple.", "useOurTools": "Use our music maker to generate instrumentals for cover songs, performances, or remixes.", "inspiringMusicStoriesDesc": "Discover what music creators worldwide are saying about woodcarvings and how our AI music generator is transforming their experience!", "vocalRemoverNav": "Vocal Remover", "saveTimeEffort": "Save Time & Effort – Create Music Instantly", "errorFileSize": "File size must be less than 10MB", "errorLoadFile": "Error loading file. Please try again.", "errorNoFile": "Please upload an audio file first", "errorProcessing": "Error processing your request. Please try again.", "errorProcessingRetry": "Processing error. Please try again later.", "errorFetchStatus": "Error checking cover status", "currentFile": "Current File:", "fileSize": "Size:", "fileUploaded": "File uploaded", "fileUploading": "File uploading, please wait...", "buttonRemoveVocals": "Remove Vocals", "titleOriginalTrack": "Original Track", "titleSeparatedTracks": "Separated Tracks", "originalTrackTitle": "Your Original Song", "errorCheckProgress": "Error checking progress. Please try again.", "vocalRemovalTitle": "How to Remove Vocals from Any Song – Fast & Easy", "vocalRemovalIntro": "With wood carvings, you can separate vocals from your audio in just a few clicks.", "step1Title": "Upload Song", "step1Action": "Upload any audio file", "step1Desc": "Support for MP3, WAV, and other formats", "step2Title": "Step 2: <PERSON><PERSON> \"Remove Vocals\"", "step2Action": "Our AI voice remover processes your file in under a minute.", "step2Desc": "Perfect for creating instrumental tracks or isolating vocals for remixes.", "step3Title": "Step 3: Preview & Download", "step3Action": "Preview the separated vocal and instrumental tracks.", "step3Desc": "Download your results and use them in your projects.", "aiVocalHeader": "Next-Gen AI Vocal Remover: Fast, Precise & Free", "aiVocalIntro": "With wood carvings, discover the most advanced AI vocal remover designed for music producers, karaoke lovers, and remix creators.", "highFidelityTitle": "High-Fidelity Sound Separation", "highFidelityDesc": "Our AI music generator with vocals uses deep learning to separate vocals and instrumentals with unmatched precision, ensuring minimal sound distortion—perfect for remixing and music production.", "instantProcessTitle": "Instant AI-Powered Processing", "instantProcessDesc": "Unlike traditional tools, our vocal remover free processes most files in seconds, making it one of the fastest online vocal removers available. No more long waiting times—get your audio processed instantly.", "perfectForTitle": "Perfect for Karaoke, Covers & Remixing", "perfectForDesc": "Whether you're a DJ, music producer, or karaoke enthusiast, our free vocal remover lets you create studio-quality instrumentals or isolate vocals effortlessly.", "secureTitle": "Secure & Private - No Data Stored", "secureDesc": "Unlike some song vocal remover tools, we prioritize privacy. Your audio files are never stored, ensuring safe and confidential processing.", "formatTitle": "Supports All Formats & URLs", "formatDesc": "Easily remove vocals from MP3 and WAV with our AI voice remover. No need to convert files before uploading.", "tryFreeTitle": "Try for Free – No Software Download Required", "tryFreeDesc": "Access our vocal remover online for free with no downloads. Test it for free and unlock premium features for high-quality vocal elimination.", "aboutFaqDesc": "Explore FAQs about our AI vocal remover tool.", "aboutUs": "About us", "aboutUsDesc": "About Us – Revolutionizing AI Music Creation", "welcomeDesc": "Welcome to woodcarvings, the ultimate AI music generator that empowers anyone to create music effortlessly. Whether you're a music enthusiast, content creator, or professional producer, our platform provides cutting-edge tools to generate custom AI songs, remove vocals, and craft original lyrics with ease.", "ourMission": "Our Mission", "missionDesc": "At woodcarvings, we aim to make music creation accessible to everyone. With our advanced AI song generator, you can create original tracks, remix existing music, or craft lyrics in just a few clicks—no prior experience needed.", "whatWeOffer": "What We Offer", "aiSongGenerator": "AI Song Generator", "aiSongGeneratorDesc": "Instantly generate unique tracks across various genres.", "vocalRemoverOnline": "Vocal Remover Online", "vocalRemoverOnlineDesc": "Extract vocals or instrumentals from any song effortlessly.", "aiLyricsGeneratorDesc": "Create custom lyrics in seconds, including rap, pop, and country.", "textToSongTechnology": "Text-to-Song Technology", "textToSongTechnologyDesc": "Transform words into music with AI-powered melody generation.", "whoWeServe": "Who We Serve", "whoWeServeDesc": "From content creators looking for royalty-free music to musicians seeking inspiration, our tools help users around the world bring their musical ideas to life.", "whyChooseUs": "Why Choose Us?", "aiPoweredCreativity": "AI-Powered Creativity", "aiPoweredCreativityDesc": "Generate music and lyrics with state-of-the-art AI.", "easyAndFast": "Easy & Fast", "easyAndFastDesc": "No need for expensive software—create songs in seconds.", "multiGenreSupport": "Multi-Genre Support", "multiGenreSupportDesc": "From rap to country, explore diverse musical styles.", "freeAndPremiumOptions": "Free & Premium Options", "freeAndPremiumOptionsDesc": "Start with free trials and unlock advanced features.", "joinUs": "Free & Premium Options", "create2": "AI Music Generator | Create AI Songs from Text & Lyrics | wood carvings", "contactSeoTitle": "Contact wood carvings | AI Music Generation Support & Help", "aboutSeoTitle": "About wood carvings | Leading AI Music Generation Platform & Technology", "aboutT": "wood carvings | Contact", "aboutD": "wood carvings Contact", "myWorks": "My Works", "musicCreation": "Create Music", "redirectSubscription": "You will be redirected to the subscription page, please click OK to continue", "cryptoPayment": "Cryptocurrency - BSC Chain, USDT Payment", "credits": "CREDITS", "unlimited": "Unlimited", "usdtPrice": "USDT", "popularPlan": "Popular", "selected": "Selected", "selectPlan": "Select plan", "poemCreationInfo": "Create up to {count} poems (3 credits per poem)", "unlimitedPoemCreationInfo": "Create unlimited poems", "disclaimer": "Lyrics, titles, and styles should not contain names of famous people or inappropriate words.", "creditsCostInfo": "Generating once costs 3 credits", "paymentTitle": "Payment with USDT on", "balance": "Balance:", "refreshBalance": "Refresh balance", "paymentCompleted": "Payment completed successfully!", "connectWallet": "Connect wallet", "connecting": "Connecting...", "pay": "Pay", "processing": "Processing...", "telegramHelp": "Subscription: contact for help in Telegram group", "viewTransaction": "View transaction", "insufficientBalance": "Insufficient USDT. You have {available} USDT, you need {required} USDT", "connectWalletError": "Please connect your wallet first", "switchNetworkError": "Failed to switch to {network}", "balanceWarning": "Make sure you have enough USDT in your wallet before payment", "emailTemplate": {"greeting": "Hello, ", "verificationCodeRequest": "You've requested a verification code for your AIMakeSong account. Please use the code below to complete your action:", "codeExpiration": "This code will expire in 5 minutes. If you didn't request this code, please ignore this email or contact us if you have concerns.", "regards": "Best regards,", "team": "The AIMakeSong Team", "rights": "All rights reserved.", "support": "Contact Support", "privacy": "Privacy Policy", "autoMessage": "This is an automated message, please do not reply directly to this email."}, "continueWithGoogle": "Continue with Google", "verificationCodeSentTo": "Verification code sent to:", "enterReceivedVerificationCode": "Please enter the verification code you received", "resendVerifyCode": "resend", "useAnotherEmail": "Use another email", "shareMusic": "Share Music", "shareMusicDesc": "Share your music creations with friends", "noImage": "No Image", "musicId": "Music ID", "shareLink": "Share Link", "openSharePage": "Open Share Page", "nowPlaying": "Now Playing", "showLyrics": "Show Lyrics", "hideLyrics": "Hide Lyrics", "aiGeneratedMusic": "AI-Generated Music", "noLyrics": "No lyrics available", "lyrics": "Lyrics", "unknown": "Unknown", "createdBy": "Created by: ", "download": "Download", "createMusic": "Create Music", "loading": "Loading...", "musicNotFound": "Music Not Found", "musicNotFoundDesc": "The requested music could not be found. It may have been deleted or does not exist.", "checkOutThisMusic": "🎉 Just discovered an incredible AI-generated song! The vibe is amazing~ Check it out!", "share-metadata": {"siteName": "wood carvings", "shareMusic": {"title": "Share Your AI Music Creation in One Click", "description": "Let the world hear your musical talent", "keywords": "Our new music sharing feature lets you easily showcase your AI creations and gain attention and appreciation from global audiences."}}, "shareFailed": "Share Failed", "sharing": "Sharing...", "shareFeatures": {"oneClickShare": "One-click sharing feature", "oneClickDesc": "With just one click, share your musical works to social media platforms such as Facebook, X, Instagram, allowing your creations to instantly reach a global audience.", "uniqueLink": "Exclusive sharing link", "uniqueLinkDesc": "Get a unique permanent link to conveniently share your music in any situation. Whether through email, text message, or instant messaging apps, you can easily deliver your music.", "customCard": "Customized sharing card", "customCardDesc": "Personalize your sharing preview image, add title, cover and description, making your music stand out on social media and attracting more audience clicks and interactions.", "qrCode": "QR code generation", "qrCodeDesc": "Create exclusive QR codes for your music, suitable for printing on business cards, posters or promotional materials, making offline promotion simple and efficient."}, "subscriptionSuccessful": "Subscription Successful!", "thankYouForSubscribing": "Thank you for subscribing to", "accessToPremiumFeatures": "You now have access to all premium features!", "paymentProcessing": "Payment Processing", "paymentProcessingDesc": "Your payment is still being processed. Please check your membership status in your profile page. If you don't see any changes, please contact our support.", "goToProfile": "Go to Profile", "processingSubscription": "Processing your subscription...", "socialShare": "Social Share", "selectPaymentMethod": "Select Payment Method", "other": "Other", "recommended": "Recommended", "model": "Model", "advancedMusicModel": "Our advanced music generation model", "extended8MinMusicGen": "⏱️ Extended 8-minute music generation", "singleSongMultiStyleConv": "🔄 Multi-style conversion for a single song", "smarterPromptUnderstand": "🧠 Smarter prompt understanding", "clearerStyleMix": "🎨 Clearer style mixing", "upgradeNeeded": "Upgrade Required", "upgradeToV2": "Upgrade to experience v2.0 of our AI Song Generator with enhanced features.", "aiMakeSong2Features": "AIMakeSong 2.0 Features", "extended8MinMusic": "Extended 8-minute music generation - enough for complete songs", "multiStyleConversion": "Multi-style conversion - switch between rock, folk, electronic styles with one click", "smarterPromptUnderstanding": "Smarter prompt understanding - accurately captures your creative intent", "clearerStyleMixing": "Clearer style mixing - precise control over music element combinations", "lifetimePrivilege": "Lifetime Privilege", "subscribeOncePermanentAccess": "Subscribe once to permanently access all models and future features", "upgradePlan": "Upgrade Plan", "cancelAnytime": "1. Cancel anytime. No hidden fees.", "unusedCreditsRollOver": "2. Unused credits roll over to the next month and remain available even after cancellation.", "newVersion": "NEW VERSION", "introducingAIMakeSong2": "Introducing AIMakeSong 2.0", "mostAdvancedTechYet": "Our most advanced AI music technology yet. Create longer songs, switch between styles, and enjoy smarter creative assistance.", "extended8MinuteSongs": "Extended 8-Minute Songs", "createCompleteCompositions": "Create complete compositions with intros, verses, choruses, and outros in a single generation.", "multiStyleConversionFeature": "Multi-Style Conversion", "transformYourSong": "Transform your song between rock, folk, electronic and more styles with a single click.", "smarterPromptUnderstandingFeature": "Smarter Prompt Understanding", "aiNowBetterCaptures": "Our AI now better captures your creative vision, understanding complex instructions and musical concepts.", "clearStyleMixingFeature": "Clear Style Mixing", "preciselyControlElements": "Precisely control how different musical elements combine, creating your signature sound with unprecedented clarity.", "tryAIMakeSong2Now": "Try AIMakeSong 2.0 Now", "premiumFeatureSubscribeOnce": "Premium feature. Subscribe once for lifetime access to all current and future features.", "frequentlyAskedQuestions": "Frequently Asked Questions", "whatsNewInAIMakeSong2": "What's new in AIMakeSong 2.0?", "aimakesong2IntroducesFull": "AIMakeSong 2.0 introduces extended 8-minute song generation, multi-style conversion capabilities, smarter prompt understanding, and clearer style mixing technology. These improvements allow for more complete songs and greater creative control.", "doINeedToPayForAccess": "Do I need to pay to access AIMakeSong 2.0?", "yesAIMakeSong2IsAvailable": "Yes, AIMakeSong 2.0 is available to our premium subscribers. With a one-time subscription, you'll permanently gain access to all models and future features as they're released.", "howMuchLongerAreSongs": "How much longer are the songs in version 2.0?", "aimakesong2CanGenerate": "AIMakeSong 2.0 can generate songs up to 8 minutes in length, compared to the previous version's limit. This allows for complete musical compositions with proper intros, verses, choruses, bridges, and outros.", "script": {"releaseNotes": "Extended song duration, multi-style conversion, improved prompt understanding", "description": "AI-powered music generation tool with extended 8-minute songs, multi-style conversion, and smarter prompt understanding.", "feature1": "Extended 8-minute music generation", "feature2": "Multi-style song conversion", "feature3": "Smarter prompt understanding", "feature4": "Clear style mixing technology", "error": "Generation failed, please try again later"}, "translation": "AIYONG TECHNOLOGY CO., LIMITED, RM Al, 11/F, WINNER BUILDING, 36 MAN YUE STREET, HUNG HOM HONG KONG", "uploadPromptMobile": "Select your files and upload", "limitedTimeOffer": "Limited Time Offer", "homepage": "Homepage", "blogtitle": "Our Blog", "blogdesc": "Discover insights, tips, and the latest news about AI music generation", "checkFileFormat": "Unsupported file format. Please try a different file.", "blogSeo": {"siteTitle": "How to Make Music & Music Production Tips | AIMakeSong", "siteDescription": "Free music production tutorials, songwriting tips, AI music creation guides, and mixing advice. Learn how to make professional music with AI tools, DAW basics, and music theory essentials.", "keywords": "music production tips, how to make beats, songwriting tips, music mixing guide, free music tutorials, home studio setup, music theory basics, ai music creation, beat making tips, music production for beginners, vocal recording tips, music arrangement tips, music mixing basics, daw tutorials, music composition guide", "checkFileFormat": "Please check if the uploaded file format is correct", "howToMakeMusic": "How to Make Music", "musicProductionTipsTutorials": "Music Production Tips & Tutorials", "musicProductionIntro": "Learn how to make music with our comprehensive music production tips and guides. From home studio setup to music production for beginners, discover free tutorials updated regularly. Whether you're starting your music making journey or advancing your production skills, our practical guides cover everything you need. Get professional tips, step-by-step tutorials, and expert advice to enhance your music production journey."}, "or_sign_up_with_email": "Or sign up with email", "email_address": "Email address", "sign_up": "Sign up", "table_of_contents": "Table of Contents", "back_to_blog": "Back to blog", "labubu": {"androidDynamic": "Android Dynamic", "interactiveEffects": "Interactive Effects", "musicRhythm": "Music Rhythm", "iosDynamic": "iOS Dynamic", "nightLight": "Night Light", "seasonal": "Seasonal", "desktopDynamic": "Desktop Dynamic", "screensaver": "Screensaver", "hdScene": "HD Scene", "pleaseLoginToGetExclusiveWallpapers": "Please login to get exclusive wallpapers", "subscriptionRequiredToGetExclusiveWallpapers": "Subscription required to get exclusive wallpapers", "preparingDownloadPleaseWait": "Preparing download, please wait...", "downloadSuccessfulPleaseCheckYourDownloadsFolder": "Download successful! Please check your downloads folder", "gift": "Gift", "labubuDynamicWallpapers": "Labubu Dynamic Wallpapers", "subscribeNowForFreeWallpapers": "Subscribe now for free wallpapers:", "hdDynamicWallpapers": "HD Dynamic Wallpapers", "exclusiveSoundEffects": "Exclusive Sound Effects", "tenPlusThemeScenes": "10+ Theme Scenes", "wallpaperPreview": "Wallpaper Preview:", "mobileWallpapers": "Mobile Wallpapers", "dynamicWallpapers": "Dynamic Wallpapers", "desktopWallpapers": "Desktop Wallpapers", "getNow": "Get Now", "maybeLater": "Maybe Later", "limitedTimeOfferOnlySevenDaysLeft": "Limited time offer - only 7 days left"}, "songCover1": "Song Cover", "songCover": {"title": "AI Song Cover Generator", "desc": "Turn any song into a new style while preserving its core melody.", "uploadAudioFile": "Upload Audio File", "dragAndDropOrBrowse": "Drag & drop an audio file or click to browse", "supportedFormats": "Supported formats: MP3, WAV, OGG, M4A", "maxSize": "Maximum file size: 10MB", "chooseFile": "Choose <PERSON>", "uploading": "Uploading...", "currentFile": "Current File", "fileSize": "File Size", "uploadNewFile": "Upload New File", "originalAudio": "Original Audio", "browserNotSupport": "Your browser does not support audio playback", "errorFileSize": "File size exceeds the 10MB limit", "errorProcessingRetry": "Error processing file. Please try again.", "defaultMode": "Simple Mode", "customMode": "Custom Mode", "instrumentalOnly": "Instrumental Only", "noOriginalAudio": "Please upload an audio file first"}, "coverAbout": "It takes about 20-40 seconds to generate, please wait a moment.", "songCoverIntro": "Utilizes a streaming response system, delivering results in as fast as 20 seconds.", "coverPageTitle": "AI Song Cover Generator - Create Amazing Cover Songs", "coverPageDesc": "Transform any song into a new style with our AI-powered cover song generator. Change genres, vocals, and more!", "coverPageKeywords": "ai song cover, cover song, ai voice cover, music style transfer, song remake", "coverAI": "AI Song Cover Generator", "coverMusicDesc": "The fastest and easiest way to create an amazing AI cover of your favourite songs with any voice ", "selectStyle": "Select Music Style", "searchStyles": "Search styles...", "selectVoice": "Select Voice", "allVoices": "All Voices", "maleVoices": "Male", "femaleVoices": "Female", "autoVoice": "Auto", "enableAdvancedMode": "Advanced Mode", "advancedModeDesc": "Unlock additional customization options for your cover", "uploadCoverPrompt": "Click to upload or drag & drop your audio file here", "buttonGenerateCover": "Generate Cover", "uploadNewFile": "Upload New File", "defaultMode": "Default Mode", "customMode": "Custom Mode", "titleGeneratedCover": "Generated Cover", "errorNoStyle": "Please select a music style", "coverTitle": "AI Song Cover Generator", "coverIntro": "Transform any song into different styles with cutting-edge AI technology. Create covers with new voices, instruments, and genres.", "howToCoverTitle": "How to Make Your AI Song Cover?", "step2TitleCover": "Choose Style", "step2ActionCover": "Select musical style and voice", "step2DescCover": "Customize your cover's sound", "copyright": "Due to copyright restrictions, this song cannot be sung", "step3TitleCover": "Generate", "step3ActionCover": "AI creates your new cover", "step3DescCover": "Preserve melody while transforming style", "step4Title": "Download", "step4Action": "Get your new cover", "step4Desc": "Use it anywhere, royalty-free", "coverFeaturesTitle": "Why Use Our AI Cover Generator", "coverFeaturesSubtitle": "Transform your favorite songs with powerful AI technology", "coverFeature1Title": "Style Transfer", "coverFeature1Desc": "Change any song into a completely different genre or style", "coverFeature2Title": "Voice Transformation", "coverFeature2Desc": "Create covers with different vocalist styles and tones", "coverFeature3Title": "High Quality", "coverFeature3Desc": "Professional-grade audio quality for your cover songs", "coverFeature4Title": "Fast Processing", "coverFeature4Desc": "Get your AI-generated covers in minutes", "coverPromoTitle": "Ready to Create Amazing Covers?", "coverPromoDesc": "Start transforming your favorite songs today with our AI Cover Generator", "coverPromoButton": "Get Started Now", "coverFaqTitle": "Frequently Asked Questions", "coverFaqSubtitle": "Common questions about our AI Song Cover Generator", "faqCover1Question": "How does the AI Song Cover Generator work?", "faqCover1Answer": "Our AI analyzes your uploaded song, identifies key musical elements, and then recreates it in your chosen style while preserving the original melody and structure. The advanced neural networks handle everything from instrumental changes to vocal transformations.", "faqCover2Question": "What music styles are available?", "faqCover2Answer": "We offer a wide range of styles including pop, rock, EDM, R&B, jazz, classical, country, metal, folk, hip-hop, reggae, and blues. Each style comes with distinct instrumental and vocal characteristics.", "faqCover3Question": "Can I change the singer's voice?", "faqCover3Answer": "Yes! You can select from various male, female, and specialty voices to transform the vocals. You can even use the Auto option to let our AI determine the best voice for your chosen style.", "faqCover4Question": "What audio formats are supported?", "faqCover4Answer": "Our AI Cover Generator supports most common audio formats including MP3, WAV, AAC, FLAC, and OGG. Files should be under 10MB and preferably of good audio quality for best results.", "faqCover5Question": "Can I use the generated covers commercially?", "faqCover5Answer": "While our AI-generated covers are royalty-free in terms of our service, you must respect the original song's copyright. Using covers of copyrighted songs may require licensing from the original rights holders for commercial use.", "stillHaveQuestions": "Still have questions?", "contactSupport": "Contact our support team", "coverPageMainTitle": "Make AI Cover Songs with Your Favourite Voices!", "coverPageMetaDesc": "Create AI song covers with your favorite voices in seconds. No extra skills needed—start making unique covers now!", "coverMainBanner": "The fastest and easiest way to create an amazing AI cover of your favourite songs with any voice.", "coverStatsTitle": "2000+ AI Cover Songs Have Been Created Here", "coverWhyLoveTitle": "Why You'll Love Our AI Song Cover Tool?", "coverWhyLovePoint1": "Vast library of voices, including famous artists.", "coverWhyLoveDesc1": "Choose from an extensive collection of voices, featuring both unique AI-generated options and beloved famous artists. Whether you're after a fresh, original sound or the iconic tone of a well-known singer, our library has the perfect voice to bring your cover to life.", "coverWhyLovePoint2": "Support for any song – just provide the audio or lyrics.", "coverWhyLoveDesc2": "Bring any song to life with our AI cover tool. Simply upload the audio file or input the lyrics, and our advanced technology will handle the rest. No song is off-limits—your creativity is the only boundary.", "coverWhyLovePoint3": "Customization options for style and effects.", "coverWhyLoveDesc3": "Make your cover truly yours with a range of customization options. Adjust the musical style to fit your vision and add special effects to enhance the sound. From subtle tweaks to bold transformations, the choice is yours to create a cover that stands out.", "coverWhyLovePoint4": "Easy sharing on social media platforms.", "coverWhyLoveDesc4": "Share your masterpiece with the world in just a few clicks. Our tool makes it effortless to post your AI cover songs directly to your favorite social media platforms. Show off your creativity and connect with fellow music lovers.", "coverHowToTitle": "How to Make Your AI Song Cover? 3 Easy Steps!", "coverHowToStep1": "Choose a voice from our library or upload your own.", "coverHowToStep2": "Select the song you want to cover.", "coverHowToStep3": "Let our AI generate the cover song.", "coverHowToStep4": "Download or share your masterpiece.", "coverTestimonialsTitle": "Hear From Our Satisfied Users", "coverTestimonial1": "I couldn't believe it when I heard my cover in <PERSON>'s voice! It was like a dream come true. The library of voices is incredible—there are so many options, including my favorite artists. This tool made me feel like a real music producer!", "coverTestimonial2": "Honestly, not tech-savvy or musician at all, but this tool was so easy to use. I had my first cover done in minutes! The step-by-step process is super simple, and I didn't need any special skills. If I can do it, anyone can!", "coverTestimonial3": "love how I can tweak the style to match my vision. The customization options let me experiment with different effects and make each cover unique. It's like having a personal music studio at my fingertips!", "coverTestimonial4": "Sharing my covers on Instagram has been a blast. My friends can't believe I made them! The easy sharing feature makes it so simple to post directly to social media. It's a great way to show off my creativity.", "coverTestimonial5": "This tool has reignited my love for music. It's so much fun to experiment with different voices and styles. I've created covers I never thought possible, and the whole experience is just pure joy!", "coverFaqSectionTitle": "You May Also Want to Know", "coverFaqQuestion1": "How does the AI technology work?", "coverFaqAnswer1": "Our AI technology uses advanced machine learning algorithms to analyze and replicate voices. It learns from a vast dataset of vocal samples to understand the unique characteristics of different voices. When you select a voice and a song, the AI applies these voice characteristics to the song, creating a unique cover that sounds like it was sung by the chosen voice. The process is seamless and happens in just a few clicks!", "coverFaqQuestion2": "Can I use my own voice for the cover?", "coverFaqAnswer2": "Yes, you can use your own voice! Simply upload a clear audio sample of your voice, and our AI will process it to create a personalized voice model. Once your voice model is ready, you can use it to generate covers of any song in your own voice. It's a fun way to hear yourself sing your favorite tracks, even if you're not a professional singer.", "coverFaqQuestion3": "Are there any copyright issues with sharing the covers?", "coverFaqAnswer3": "When sharing AI-generated covers, especially of copyrighted songs, there may be legal considerations. We recommend using songs that are in the public domain or obtaining permission from the copyright holders before sharing. Always check the terms of use for the songs you choose to ensure you're respecting copyright laws. Our tool is for personal and creative use, so be mindful when sharing publicly.", "coverFaqQuestion4": "Do I need any special software or skills?", "coverFaqAnswer4": "No special software or skills are required! Our tool is designed to be user-friendly, with a simple interface that guides you through each step of the process. All you need is a device with internet access and your creativity. Whether you're a music enthusiast or a complete beginner, you'll find it easy to create and enjoy your AI song covers.", "LYRICS_GENERATOR_TEXT": {"getAnswersToMostCommonQuestions": "Get answers to the most common questions about our AI-powered lyrics generator.", "howDoesLyricsToSongAiToolWork": "How does the lyrics to song ai tool actually work?", "platformUsesSophisticatedLanguageModel": "Our platform uses a sophisticated language model trained on a massive dataset. When you provide a prompt, the ai lyrics generator analyzes it and predicts lyrical sequences that fit your criteria, ensuring originality and coherence for your song writing.", "isRapLyricsGeneratorAiSuitableForProfessionalArtists": "Is this rap lyrics generator ai suitable for professional artists?", "absolutelyManyProfessionalsUse": "Absolutely. Many professionals use our ai rap lyrics generator to brainstorm ideas and break creative blocks. It's a powerful assistant that enhances your unique talent, and all your work is stored securely and privately.", "whatMakesYourSongWritingToolBestChoice": "What makes your song writing tool the best choice?", "wePrioritizeContextualRelevance": "We prioritize contextual relevance, user privacy with free storage, and genre-specific nuances. Our song writing ai is not just about random words; it's about efficiently crafting a narrative that resonates with your vision.", "canIGetIdeasForSongsAboutWritingSongs": "Can I get ideas for songs about writing songs?", "yesItsGreatMetaExercise": "Yes, it's a great meta-exercise! You can prompt the ai lyrics generator with themes of creativity or inspiration, and it will produce unique lyrics about the very act of song writing itself, helping you find a new perspective.", "stillHaveQuestions": "Still Have Questions?"}, "menu": {"aiLyrics": "AI Lyrics", "tools": "Tools", "lyricsToSong": "Lyrics to Song", "lyricsToSongDesc": "Transform lyric to song", "textToSong": "Text to Song", "textToSongDesc": "Convert text to song", "aiLyricsGenerator": "AI Lyrics Generator", "aiLyricsGeneratorDesc": "AI Song Lyrics Generator", "extendSong": "Extend Song", "extendSongDesc": "Extend song with AI", "vocalRemover": "Vocal Remover", "vocalRemoverDesc": "Remove vocals from a song", "songCover": "Song Cover", "songCoverDesc": "Create AI covers with different voices"}, "rapSongMakerDesc": "Our rap song maker and melody generator assist in crafting hip-hop, pop, and country music. The integrated ai rap lyric generator helps you overcome writer's block, while our vocal synthesis sounds like a real ai rapper, bringing your tracks to life.", "aiRapperModeDesc": "AI Rapper Mode: Generate rap vocals with authentic style and rhythm、Rap Song Maker: Complete hip-hop production with beats and flows", "testimonials": {"1": {"name": "<PERSON>", "role": "Content Creator", "content": "I always struggled to find the right background music... until I found wood carvings. Just made a hopeful-sounding track for my latest video, and everyone's asking where I got it from! 🎶", "tag0": "#IndieMusic", "tag1": "#MusicDiscovery"}, "2": {"name": "<PERSON>", "role": "Game Developer", "content": "Used AiMakesong to generate atmospheric music for my indie game. The custom instrumental mode gave me exactly the mysterious vibe I needed. Saved thousands on music production!"}, "3": {"name": "<PERSON>", "role": "Podcast Host", "content": "I've been creating ambient music for years, and wood carvings's generated tracks are exactly what I needed. The spatial audio quality and stereo field created by the AI are surprisingly impressive—saved me hours of post-processing! 🎧", "tag0": "#ProducerLife"}, "4": {"name": "<PERSON>", "role": "TikTok Influencer", "content": "My TikTok views doubled after using AiMakesong's custom tracks! The 30-second music snippets are perfect for my content, and the style variety keeps things fresh."}, "5": {"name": "<PERSON>", "role": "Independent Musician", "content": "Say goodbye to copyright headaches! The music wood carvings generates sounds so natural—none of that robotic AI vibe. It's perfect for my podcast intros. 🎵", "tag0": "#ContentCreator"}, "6": {"name": "<PERSON>.", "role": "Wedding Filmmaker", "content": "Generated custom romantic tracks for highlight reels. Clients love how the music enhances their special moments. No more generic wedding music!"}, "7": {"name": "<PERSON>.", "role": "Hip-Hop Producer", "content": "Best AI rap lyric generator I've found! As a producer, this free musician website saved me hours. The rap song maker feature creates beats that actually slap. My AI rapper tracks are getting thousands of plays! 🔥"}}, "aiMakeSong": {"description": "wood carvings is your ultimate AI song generator and music maker, designed to help anyone create professional-quality AI music free in minutes. Think of it as your personal ai rapper and producer in one. Input a brief song idea, adjust lyrics, choose a title, and pick a style -- our AI music generator tool will create a full, original track for you in seconds. Whether you're a beginner or a pro, our platform serves as a comprehensive rap song maker that makes music creation simple, intuitive, and enjoyable. With our advanced ai rap lyric generator, you can craft perfect rap lyrics, theme music for suits lyrics, or any genre you desire!"}}