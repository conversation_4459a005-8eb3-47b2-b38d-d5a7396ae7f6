import { Google, generateCodeVerifier, generateState } from 'arctic'
import { getBaseUrl } from 'utils'
// import { NextApiResponse } from 'next'
import { redirect } from 'next/navigation'
import { NextRequest, NextResponse } from 'next/server'
import {
  createOauthCallbackHandler,
  createOauthRedirectHandler,
} from '../lib/oauth'
import { supabase } from '../../../apps/web/app/lib/supabaseClient'
import { logger } from 'logs'

const CALLBACK_URL =
  process.env.NODE_ENV === 'production'
    ? 'https://www.woodcarvings/api/oauth/google/callback'
    : 'http://localhost:3000/api/oauth/google/callback'

console.log(
  'faith=============process.env.GOOGLE_CLIENT_ID',
  process.env.GOOGLE_CLIENT_ID
)

const DOMAIN = 'https://your-domain.com'

export const googleAuth = new Google(
  process.env.GOOGLE_CLIENT_ID as string,
  process.env.GOOGLE_CLIENT_SECRET as string,
  CALLBACK_URL
)

const GOOGLE_PROIVDER_ID = 'google'

type GoogleUser = {
  sub: string
  email: string
  email_verified?: boolean
  picture?: string
  name: string
}
export const updateUser = async (
  username: string,
  email?: string,
  avatarUrl?: string
) => {
  const { data: existingUser, error: searchError } = await supabase
    .from('removeai_user')
    .select('*')
    .eq('email', email)
    .single()

  if (searchError && searchError.code !== 'PGRST116') {
    console.error('Error checking user:', searchError)
    throw searchError
  }

  if (existingUser) {
    const { data: updatedUser, error: updateError } = await supabase
      .from('removeai_user')
      .update({
        last_login_time: new Date().toISOString(),
        email: email || existingUser.email,
        avatar_url: avatarUrl || existingUser.avatar_url,
      })
      .eq('username', username)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating user:', updateError)
      throw updateError
    }

    return updatedUser
  }

  const { data: newUser, error: insertError } = await supabase
    .from('removeai_user')
    .insert([
      {
        username,
        email,
        avatar_url: avatarUrl,
        last_login_time: new Date().toISOString(),
        points: 4,
      },
    ])
    .select()
    .single()

  if (insertError) {
    console.error('Error creating user:', insertError)
    throw insertError
  }

  return newUser
}
export const googleRouteHandler = createOauthRedirectHandler(
  GOOGLE_PROIVDER_ID,
  () => {
    console.log('faith=============34354435345')
    console.log(
      'faith=============process.env.GOOGLE_CLIENT_ID',
      process.env.GOOGLE_CLIENT_ID
    )
    console.log(
      'faith=============process.env.GOOGLE_CLIENT_SECRET',
      process.env.GOOGLE_CLIENT_SECRET
    )
    console.log('faith=============????-GOOGLE_PROIVDER_ID', GOOGLE_PROIVDER_ID)
    const state = generateState()
    const codeVerifier = generateCodeVerifier()
    // console.log('faith=============state-codeVerifier', state, codeVerifier)
    const url = googleAuth.createAuthorizationURL(state, codeVerifier, [
      'profile',
      'email',
    ])

    // console.log('faith=============url', url)

    return {
      state,
      url,
      codeVerifier,
    }
  }
)

export const googleCallbackRouteHandler = createOauthCallbackHandler(
  GOOGLE_PROIVDER_ID,
  async (code, verifier) => {
    console.log('faith=============code-verifier-222', code, verifier)
    console.log('faith=============code-verifier-222', code, verifier)
    console.log('faith=============code-verifier-222', code, verifier)
    console.log('faith=============code-verifier-222', code, verifier)
    console.log('faith=============code-verifier-222', code, verifier)

    console.log(
      'faith=============process.env.GOOGLE_CLIENT_ID',
      process.env.GOOGLE_CLIENT_ID
    )
    console.log(
      'faith=============process.env.GOOGLE_CLIENT_SECRET',
      process.env.GOOGLE_CLIENT_SECRET
    )
    console.log('faith=============CALLBACK_URL', CALLBACK_URL)

    // 3. 请求 token
    // 本地 start
    const response = await fetch(
      'https://www.woodcarvings/api/oauth/google/validate',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code,
          verifier,
        }),
      }
    )

    const { googleUser } = await response.json()
    // console.log('faith=============response-tokens---wwwww', tokens)
    // console.log('faith=============response-tokens---tokens', response)
    console.log('faith=============tokens---tokens', googleUser)

    // 本地 end

    // 生产 start

    // const googleAuth2 = new Google(process.env.GOOGLE_CLIENT_ID as string, process.env.GOOGLE_CLIENT_SECRET as string, CALLBACK_URL)
    // const tokens = await googleAuth2.validateAuthorizationCode(code, verifier as string)

    // const googleUserResponse = await fetch('https://openidconnect.googleapis.com/v1/userinfo', {
    //   headers: {
    //     Authorization: `Bearer ${tokens.accessToken()}`
    //   }
    // })
    // 存入用户信息
    // const googleUser = (await googleUserResponse.json()) as GoogleUser
    // 生产 end

    try {
      updateUser(googleUser.name, googleUser.email, googleUser.picture)
    } catch (error) {
      logger.error('更新用户信息失败：', error)
    }
    return {
      id: googleUser.sub,
      email: googleUser.email,
      name: googleUser.name,
      avatar: googleUser.picture,
    }
  }
)

// export async function googleCallbackRouteHandler(request: NextRequest) {
//   try {
//     const searchParams = request.nextUrl.searchParams
//     const code = searchParams.get('code')
//     const verifier = searchParams.get('verifier')

//     if (!code || !verifier) {
//       return new Response(null, {
//         status: 302,
//         headers: {
//           Location: '/auth/error?error=missing_parameters'
//         }
//       })
//     }

//     const tokens = await googleAuth.validateAuthorizationCode(code, verifier)

//     console.log('faith=============tokenstokens', tokens)

//     const googleUserResponse = await fetch('https://openidconnect.googleapis.com/v1/userinfo', {
//       headers: {
//         Authorization: `Bearer ${tokens.accessToken()}`
//       }
//     })

//     if (!googleUserResponse.ok) {
//       throw new Error('Failed to fetch user info')
//     }

//     const googleUser = (await googleUserResponse.json()) as GoogleUser

//     const user = {
//       id: googleUser.sub,
//       email: googleUser.email,
//       name: googleUser.name,
//       avatar: googleUser.picture
//     }

//     console.log('faith=============user', user)

//     // 处理认证成功的情况，这里可以根据需要修改重定向地址和参数
//     return new Response(null, {
//       status: 302,
//       headers: {
//         Location: `/auth/success?email=${encodeURIComponent(user.email)}&name=${encodeURIComponent(user.name)}`
//       }
//     })
//   } catch (error) {
//     console.error('Google OAuth callback error:', error)
//     return new Response(null, {
//       status: 302,
//       headers: {
//         Location: '/auth/error?error=authentication_failed'
//       }
//     })
//   }
// }
