{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "globalEnv": ["NODE_ENV", "PORT", "DATABASE_URL", "NEXT_PUBLIC_SITE_URL", "VERCEL_URL", "LEMONSQUEEZY_WEBHOOK_SECRET", "LEMONSQUEEZY_API_KEY", "STRIPE_SECRET_KEY", "STRIPE_WEBHOOK_SECRET", "CHARGEBEE_API_KEY", "CHARGEBEE_SITE", "MAIL_HOST", "MAIL_PORT", "MAIL_USER", "MAIL_PASS", "GITHUB_CLIENT_ID", "GITHUB_CLIENT_SECRET", "GOOGLE_CLIENT_ID", "GOOGLE_CLIENT_SECRET", "S3_ENDPOINT", "S3_ACCESS_KEY_ID", "S3_SECRET_ACCESS_KEY", "PLUNK_API_KEY", "RESEND_API_KEY", "POSTMARK_SERVER_TOKEN", "NEXT_PUBLIC_AVATARS_BUCKET_NAME"], "tasks": {"build": {"dependsOn": ["^db:generate", "^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "type-check": {}, "clean": {"cache": false}, "db:generate": {"cache": false}, "db:push": {"cache": false}, "dev": {"cache": false, "dependsOn": ["^db:generate"], "persistent": true}, "export": {"outputs": ["out/**"]}, "lint": {}}}