# Database
DATABASE_URL="YOUR_DATABASE_CONNECTION_STRING"
# ... if you use Supabase
MIGRATION_DATABASE_URL=""

# Site url
NEXT_PUBLIC_SITE_URL="http://localhost:3000"

# 
# Payments
# ... with Lemonsqueezy
LEMONSQUEEZY_API_KEY=""
LEMONSQUEEZY_WEBHOOK_SECRET=""
LEMONSQUEEZY_STORE_ID=""
# ... with Stripe
STRIPE_SECRET_KEY=""
STRIPE_WEBHOOK_SECRET=""
# ... with Chargebee
CHARGEBEE_SITE=""
CHARGEBEE_API_KEY=""

# Mailing
# ... with nodemailer
MAIL_HOST="smtp.zoho.jp"
MAIL_PORT=587
MAIL_USER="<EMAIL>"
MAIL_PASS="vZ6URGXpFz2"
# ... with Plunk
PLUNK_API_KEY=""

# Analytics
# ... for Pirsch
NEXT_PUBLIC_PIRSCH_CODE=""
# ... for Plausible
NEXT_PUBLIC_PLAUSIBLE_URL=""
# ... for Mixpanel
NEXT_PUBLIC_MIXPANEL_TOKEN=""
# ... for Google Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=""

# 

# Supabase
NEXT_PUBLIC_SUPABASE_URL = "https://jrgocbhuubiqyvrbsqut.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpyZ29jYmh1dWJpcXl2cmJzcXV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQwNTY5NjksImV4cCI6MjA0OTYzMjk2OX0.jGJHo_ekbWCmh8kHePVBtboGVEZeA7l98djB-6AebdM"


# Authentication
# ... for Github
GITHUB_CLIENT_ID="YOUR_GITHUB_CLIENT_ID"
GITHUB_CLIENT_SECRET="YOUR_GITHUB_CLIENT_SECRET"
# ... for Google
GOOGLE_CLIENT_ID="216697875560-bb9669u1bb4t94pj2gflk09e3lnne8b3.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"
NEXT_PUBLIC_GOOGLE_CLIENT_ID="216697875560-bb9669u1bb4t94pj2gflk09e3lnne8b3.apps.googleusercontent.com"

# Storage
S3_ACCESS_KEY_ID=""
S3_SECRET_ACCESS_KEY=""
S3_ENDPOINT=""
NEXT_PUBLIC_AVATARS_BUCKET_NAME="avatars"

PAYPRO_VALIDATION_KEY="yB2jNz!QRKq7Dnak200MtBXJhhVW"
NEXT_PUBLIC_AB= "sb_publisha_5wXIJ7f71Gv2WNFqJa-9rg_I3ehU4UB"

# 
# AI
# ... with OpenAI
OPENAI_API_KEY=""

NEXT_PUBLIC_TURNSTILE_SITE_KEY="04AAAAAABTwSQatTdivSi5-"
TURNSTILE_SECRET_KEY="0x4AAAAAATwSRWpbx1ujxx1vPHsRkC6qy8"


GOOGLE_CLOUD_PROJECT=imggen-465509

