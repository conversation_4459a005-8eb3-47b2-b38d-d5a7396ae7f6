import variablesPlugin from "@mertasan/tailwindcss-variables";
import colorVariable from "@mertasan/tailwindcss-variables/colorVariable";
import containerQueryPlugin from "@tailwindcss/container-queries";
import formsPlugin from "@tailwindcss/forms";
import typographyPlugin from "@tailwindcss/typography";
import type { Config } from "tailwindcss";
import animatePlugin from "tailwindcss-animate";

export const lightVariables = {
	colors: {
		border: "#d8dee6",
		input: "#c7ced8",
		ring: "#4e6df5",
		background: "#f5f5f8",
		foreground: "#292b35",
		primary: "#4e6df5",
		"primary-foreground": "#f6f7f9",
		secondary: "#292b35",
		"secondary-foreground": "#ffffff",
		destructive: "#ef4444",
		"destructive-foreground": "#ffffff",
		success: "#39a561",
		"success-foreground": "#ffffff",
		muted: "#f8fafc",
		"muted-foreground": "#64748b",
		accent: "#ddddea",
		"accent-foreground": "#292b35",
		popover: "#ffffff",
		"popover-foreground": "#292b35",
		card: "#ffffff",
		"card-foreground": "#292b35",
		highlight: "#e5a158",
		"highlight-foreground": "#ffffff",
	},
};

export const darkVariables = {
	colors: {
		border: "#282c34",
		input: "#333741",
		ring: "#5581f7",
		background: "#12181d",
		foreground: "#e9eef3",
		primary: "#5581f7",
		"primary-foreground": "#091521",
		secondary: "#e9eef3",
		"secondary-foreground": "#091521",
		destructive: "#ef4444",
		"destructive-foreground": "#ffffff",
		success: "#39a561",
		"success-foreground": "#ffffff",
		muted: "#020817",
		"muted-foreground": "#94a3b8",
		accent: "#1e293b",
		"accent-foreground": "#f8fafc",
		popover: "#0d1116",
		"popover-foreground": "#e9eef3",
		card: "#0d1116",
		"card-foreground": "#e9eef3",
		highlight: "#e5a158",
		"highlight-foreground": "#ffffff",
	},
};

export default {
	content: [],
	darkMode: ["class"],
	theme: {
		container: {
			center: true,
			padding: "2rem",
			screens: {
				"2xl": "1280px",
			},
		},
		extend: {
			boxShadow: {
				sm: "0 2px 8px 0 rgb(0, 0, 0, 0.025), 0 0 1px rgba(0,0,0,0.1)",
				DEFAULT: "0 4px 16px 0 rgb(0, 0, 0, 0.05), 0 0 1px rgba(0,0,0,0.1)",
				md: "0 6px 24px 0 rgb(0, 0, 0, 0.075), 0 0 1px rgba(0,0,0,0.1)",
				lg: "0 8px 32px 0 rgb(0, 0, 0, 0.1), 0 0 1px rgba(0,0,0,0.1)",
				xl: "0 12px 48px 0 rgb(0, 0, 0, 0.125), 0 0 1px rgba(0,0,0,0.1)",
				"2xl": "0 16px 64px 0 rgb(0, 0, 0, 0.15), 0 0 1px rgba(0,0,0,0.1)",
			},
			borderRadius: {
				lg: "0.75rem",
				md: "calc(0.75rem - 2px)",
				sm: "calc(0.75rem - 4px)",
			},
			fontFamily: {
				sans: ["var(--font-sans)", "sans-serif"],
			},
			keyframes: {
				"accordion-down": {
					from: { height: "0" },
					to: { height: "var(--radix-accordion-content-height)" },
				},
				"accordion-up": {
					from: { height: "var(--radix-accordion-content-height)" },
					to: { height: "0" },
				},
			},
			animation: {
				"accordion-down": "accordion-down 0.2s ease-out",
				"accordion-up": "accordion-up 0.2s ease-out",
			},
			colors: {
				border: colorVariable("--colors-border"),
				input: colorVariable("--colors-input"),
				ring: colorVariable("--colors-ring"),
				background: colorVariable("--colors-background"),
				foreground: colorVariable("--colors-foreground"),
				primary: {
					DEFAULT: colorVariable("--colors-primary"),
					foreground: colorVariable("--colors-primary-foreground"),
				},
				secondary: {
					DEFAULT: colorVariable("--colors-secondary"),
					foreground: colorVariable("--colors-secondary-foreground"),
				},
				destructive: {
					DEFAULT: colorVariable("--colors-destructive"),
					foreground: colorVariable("--colors-destructive-foreground"),
				},
				success: {
					DEFAULT: colorVariable("--colors-success"),
					foreground: colorVariable("--colors-success-foreground"),
				},
				muted: {
					DEFAULT: colorVariable("--colors-muted"),
					foreground: colorVariable("--colors-muted-foreground"),
				},
				accent: {
					DEFAULT: colorVariable("--colors-accent"),
					foreground: colorVariable("--colors-accent-foreground"),
				},
				popover: {
					DEFAULT: colorVariable("--colors-popover"),
					foreground: colorVariable("--colors-popover-foreground"),
				},
				card: {
					DEFAULT: colorVariable("--colors-card"),
					foreground: colorVariable("--colors-card-foreground"),
				},
				highlight: {
					DEFAULT: colorVariable("--colors-highlight"),
					foreground: colorVariable("--colors-highlight-foreground"),
				},
			},
		},
		variables: {
			DEFAULT: lightVariables,
		},
		darkVariables: {
			DEFAULT: darkVariables,
		},
	},
	plugins: [
		formsPlugin({
			strategy: "base",
		}),
		typographyPlugin,
		animatePlugin,
		containerQueryPlugin,
		variablesPlugin({
			colorVariables: true,
		}),
	],
} satisfies Config;
