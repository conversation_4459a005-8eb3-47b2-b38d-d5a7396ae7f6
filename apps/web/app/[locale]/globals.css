@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
}

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

pre.shiki {
  @apply mb-4 rounded-lg p-6;
}

@keyframes shine-slow {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(100%);
  }
}

@keyframes shine-fast {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(100%);
  }
}

@keyframes glow {
  0%,
  100% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(0.9);
  }
}

@keyframes particle-float {
  0% {
    transform: translateY(100%) translateX(0);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100%) translateX(20px);
    opacity: 0;
  }
}

.animate-shine-slow {
  animation: shine-slow 3s infinite;
}

.animate-shine-fast {
  animation: shine-fast 2s infinite;
}

.animate-glow {
  animation: glow 2s infinite;
}

.particles-container .particle {
  position: absolute;
  bottom: 0;
  left: 0;
}

.particles-container .particle:nth-child(1) {
  left: 10%;
}
.particles-container .particle:nth-child(2) {
  left: 30%;
}
.particles-container .particle:nth-child(3) {
  left: 50%;
}
.particles-container .particle:nth-child(4) {
  left: 70%;
}
.particles-container .particle:nth-child(5) {
  left: 90%;
}

@keyframes loadingText {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

.animate-loading {
  animation: loadingText 1.5s infinite;
}

/* 在您的 CSS 文件中添加以下内容 */
@keyframes floating {
  0% {
    transform: translateY(0) rotate(0deg);
    background-position: 0% 50%;
  }
  25% {
    transform: translateY(-3px) rotate(0.3deg);
    background-position: 50% 50%;
  }
  50% {
    transform: translateY(0) rotate(0deg);
    background-position: 100% 50%;
  }
  75% {
    transform: translateY(3px) rotate(-0.3deg);
    background-position: 50% 50%;
  }
  100% {
    transform: translateY(0) rotate(0deg);
    background-position: 0% 50%;
  }
}

.animate-floating {
  animation: floating 4s ease-in-out infinite;
  background-size: 200% auto;
}
