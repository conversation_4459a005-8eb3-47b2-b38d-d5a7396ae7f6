import { PostListItem } from '@marketing/blog/components/PostListItem'
import { allPosts } from 'content-collections'
import { getLocale, getTranslations } from 'next-intl/server'

// export async function generateMetadata() {
//   const t = await getTranslations()

//   return {
//     title: t('blogSeo.siteTitle'),
//     description: t('blogSeo.siteDescription'),
//     keywords: t('blogSeo.keywords'),
//   }
// }

// Music Production Tips  170、19
// How to Make Music 4400, 38
// Music Making Tips 110、18
// how to produce music 1000,45

export default async function BlogListPage() {
  const locale = await getLocale()
  const t = await getTranslations()

  return (
    <div className="container max-w-6xl pt-32 pb-16">
      <div className="grid gap-8 md:grid-cols-2">
        {allPosts
          .filter((post) => post.published && locale === post.locale)
          .sort(
            (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
          )
          .map((post) => (
            <PostListItem post={post} key={post.path} />
          ))}
      </div>
    </div>
  )
}
