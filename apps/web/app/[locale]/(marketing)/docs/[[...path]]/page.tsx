import { MDXContent } from '@content-collections/mdx/react'
import { redirect } from '@i18n/routing'
import { mdxComponents } from '@marketing/blog/utils/mdx-components'
import { TableOfContents } from '@marketing/shared/components/TableOfContents'
import { getActivePathFromUrlParam } from '@shared/lib/content'
import { allDocumentationPages } from 'content-collections'
import { getLocale } from 'next-intl/server'

type Params = {
  path: string | string[]
}

const decodeEscaped = (str: string) => {
  // 处理 \u{XXXX} 形式
  str = str.replace(/\\u\{([0-9A-Fa-f]+)\}/g, (_, hex) =>
    String.fromCodePoint(parseInt(hex, 16))
  )
  // 处理 \uXXXX 形式
  str = str.replace(/\\u([0-9A-Fa-f]{4})/g, (_, hex) =>
    String.fromCharCode(parseInt(hex, 16))
  )
  // 处理 \xXX 形式
  return str.replace(/\\x([0-9A-Fa-f]{2})/g, (_, hex) =>
    String.fromCharCode(parseInt(hex, 16))
  )
}

export default async function DocsPage(props: { params: Promise<Params> }) {
  const params = await props.params

  const { path } = params

  const activePath = getActivePathFromUrlParam(path)
  const locale = await getLocale()

  const page = allDocumentationPages
    .filter((page) => page.path === activePath)
    .sort((page) => (page.locale === locale ? -1 : 1))[0]

  if (!page) {
    redirect({ href: '/', locale })
  }

  const { title, subtitle, body, toc } = page

  const decodedToc = toc.map((item) => ({
    ...item,
    slug: decodeEscaped(item.slug),
    content: decodeEscaped(item.content),
  }))

  return (
    <div>
      <div className="mb-8">
        <h1 className="font-bold text-4xl">{title}</h1>

        {subtitle && (
          <p className="mt-3 text-2xl text-foreground/60">{subtitle}</p>
        )}
      </div>

      <div className="flex flex-col gap-6 lg:flex-row-reverse">
        {decodedToc.length > 0 && <TableOfContents headings={decodedToc} />}
        <div className="flex-1 pb-8">
          <MDXContent code={body} components={mdxComponents} />
        </div>
      </div>
    </div>
  )
}
