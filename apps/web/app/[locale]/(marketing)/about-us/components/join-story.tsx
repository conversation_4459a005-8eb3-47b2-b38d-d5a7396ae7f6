import Body from './typography/body'
import Heading from './typography/heading'
import { Link } from '@i18n/routing'

export default function JoinStorySection() {
  return (
    <section className="py-20 lg:py-32 bg-white relative">
      <div className="max-w-4xl mx-auto px-6 lg:px-8 text-center">
        {/* Zen-inspired section header */}
        <div className="mb-16">
          <div className="flex items-center justify-center mb-8">
            <div className="w-32 h-px bg-gray-300"></div>
            <div className="mx-8 text-2xl text-gray-400 font-light">◦</div>
            <div className="w-32 h-px bg-gray-300"></div>
          </div>

          <Heading
            className="text-gray-900 mb-8 tracking-tight"
            desktopSize="5xl"
            font="serif"
            mobileSize="2xl"
            tag="h2"
          >
            Join the Cokugoo Story
          </Heading>

          <Body
            className="text-gray-700 leading-relaxed max-w-2xl mx-auto"
            desktopSize="xl"
            font="serif"
            mobileSize="lg"
          >
            We invite you to bring a piece of this narrative into your home.
            Discover a creation that speaks to you and experience the quiet joy
            of our craft.
          </Body>
        </div>

        {/* Minimalist CTA section */}
        <div className="space-y-12">
          {/* Clean CTA card */}
          <div className="bg-gray-50 rounded-2xl p-12 lg:p-16 border border-gray-200">
            {/* Subtle wood grain symbol */}
            <div className="mb-8">
              <div className="w-16 h-16 mx-auto border border-gray-300 rounded-lg flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-gray-700"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1"
                  viewBox="0 0 32 32"
                >
                  <path d="M4 8h24M4 16h24M4 24h24" strokeLinecap="round" />
                  <path
                    d="M8 4v24M16 4v24M24 4v24"
                    opacity="0.3"
                    strokeLinecap="round"
                  />
                </svg>
              </div>
            </div>

            {/* CTA Button with minimal styling */}
            <div className="mb-8">
              <Link
                className="inline-flex items-center justify-center space-x-3 text-gray-900 hover:text-gray-700 text-lg font-semibold underline hover:no-underline transition-colors duration-300"
                href="/collections"
              >
                <span className="text-sm sm:text-lg">
                  Explore Our Collection
                </span>
                <svg
                  className="w-4 h-4 flex-shrink-0"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M17 8l4 4m0 0l-4 4m4-4H3"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </Link>
            </div>

            {/* Subtle additional info */}
            <Body
              className="text-gray-600"
              desktopSize="base"
              font="serif"
              mobileSize="sm"
            >
              Each piece is handcrafted with love and comes with its own unique
              story
            </Body>
          </div>

          {/* Final zen elements */}
          <div className="flex justify-center">
            <div className="flex items-center space-x-4">
              <div className="w-6 h-px bg-gray-300"></div>
              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
              <div className="w-12 h-px bg-gray-300"></div>
              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
              <div className="w-6 h-px bg-gray-300"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
