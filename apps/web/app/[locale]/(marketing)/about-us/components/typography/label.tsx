import { cn } from '@ui/lib'

// Define variant types
type LabelVariants = {
  font?: 'display' | 'sans' | 'serif'
  mobileSize?: '2xl' | '2xs' | '6xl' | 'base' | 'lg' | 'sm' | 'xs'
  desktopSize?: '2xl' | '2xs' | '6xl' | 'base' | 'lg' | 'sm' | 'xs'
}

// Variant mappings
const fontVariants = {
  display: 'font-display font-normal leading-[110%] uppercase',
  sans: 'font-sans font-medium leading-[110%]',
  serif: 'font-serif font-normal leading-[110%]',
}

const mobileSizeVariants = {
  '2xl': 'text-label-2xl',
  '2xs': 'text-label-2xs tracking-[0.4px]',
  '6xl': 'text-label-6xl tracking-[1.6px]',
  base: 'text-label-base tracking-[0.64px]',
  lg: 'text-label-lg tracking-[0.36px]',
  sm: 'text-label-sm tracking-[0.56px]',
  xs: 'text-label-xs tracking-[0.48px]',
}

const desktopSizeVariants = {
  '2xl': 'lg:text-label-2xl',
  '2xs': 'lg:text-body-2xs',
  '6xl': 'text-label-6xl',
  base: 'lg:text-label-base',
  lg: 'lg:text-label-lg',
  sm: 'lg:text-label-sm',
  xs: 'lg:text-label-xs',
}

// Default variants
const defaultVariants: LabelVariants = {
  font: 'serif',
  mobileSize: 'base',
}

export const labelStyles = (
  variants: LabelVariants & { className?: string }
) => {
  const { font, mobileSize, desktopSize, className } = variants

  return cn(
    fontVariants[font || defaultVariants.font!],
    mobileSizeVariants[mobileSize || defaultVariants.mobileSize!],
    desktopSize && desktopSizeVariants[desktopSize],
    className
  )
}

type LabelProps = {
  children: React.ReactNode
} & React.HTMLAttributes<HTMLDivElement> &
  LabelVariants

export default function Label({
  children,
  className,
  desktopSize,
  font,
  mobileSize,
  ...rest
}: LabelProps) {
  return (
    <div
      className={labelStyles({ className, desktopSize, font, mobileSize })}
      {...rest}
    >
      {children}
    </div>
  )
}
