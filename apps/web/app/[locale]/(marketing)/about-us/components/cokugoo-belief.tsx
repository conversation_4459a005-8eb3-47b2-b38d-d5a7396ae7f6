import Body from './typography/body'
import Heading from './typography/heading'

export default function CokugooBeliefSection() {
  return (
    <section className="py-20 lg:py-32 bg-gray-50 relative">
      <div className="max-w-5xl mx-auto px-6 lg:px-8">
        {/* Zen-inspired section header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-8">
            <div className="w-24 h-px bg-gray-300"></div>
            <div className="mx-8 w-4 h-4 border border-gray-300 rounded-full flex items-center justify-center">
              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
            </div>
            <div className="w-24 h-px bg-gray-300"></div>
          </div>

          <Heading
            className="text-gray-900 mb-12 tracking-tight"
            desktopSize="5xl"
            font="serif"
            mobileSize="2xl"
            tag="h2"
          >
            The Cokugoo Belief: From Nature to Nurture
          </Heading>
        </div>

        {/* Main philosophy content */}
        <div className="text-center mb-20">
          <Body
            className="text-gray-700 leading-relaxed max-w-3xl mx-auto"
            desktopSize="2xl"
            font="serif"
            mobileSize="lg"
          >
            We believe that every piece of wood holds a story whispered by the
            forest. Our mission is to listen to that story. The journey of
            transforming a raw block into one of our wooden animal figurines is
            a dialogue with nature itself. We strive to make sustainability and
            simplicity not just ideas, but a lived reality in every home our art
            touches.
          </Body>
        </div>

        {/* Minimalist quote section */}
        <div className="relative max-w-4xl mx-auto">
          {/* Subtle background */}
          <div className="bg-white rounded-2xl p-12 lg:p-16 border border-gray-200">
            {/* Quote text with subtle Japanese quote marks */}
            <div className="text-center mb-8">
              <div className="inline-block relative">
                <span className="text-4xl text-gray-400 font-light absolute -left-6 top-0">
                  「
                </span>
                <Body
                  className="text-gray-900 leading-relaxed italic px-8"
                  desktopSize="2xl"
                  font="serif"
                  mobileSize="xl"
                >
                  Cokugoo is where innovative design meets the soul of wood,
                  creating hand carved wooden animals that spark your
                  imagination and bring natural warmth into everyday life.
                </Body>
                <span className="text-4xl text-gray-400 font-light absolute -right-6 bottom-0">
                  」
                </span>
              </div>
            </div>

            {/* Attribution with zen styling */}
            <div className="text-center">
              <div className="w-16 h-px bg-gray-300 mx-auto mb-4"></div>
              <Body
                className="text-gray-600 font-medium tracking-wide"
                desktopSize="lg"
                font="serif"
                mobileSize="base"
              >
                The Founders of Cokugoo
              </Body>
            </div>
          </div>
        </div>

        {/* Bottom zen elements */}
        <div className="flex justify-center mt-16">
          <div className="flex items-center space-x-3">
            <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
            <div className="w-8 h-px bg-gray-300"></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
            <div className="w-8 h-px bg-gray-300"></div>
            <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
          </div>
        </div>
      </div>
    </section>
  )
}
