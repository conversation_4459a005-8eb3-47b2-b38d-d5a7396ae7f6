import { Link, redirect } from '@i18n/routing'
import { PostContent } from '@marketing/blog/components/PostContent'
import { getActivePathFromUrlParam } from '@shared/lib/content'
import { slugifyHeadline } from '@shared/lib/content'
import { allBrandStories } from 'content-collections'
import { getLocale, getTranslations } from 'next-intl/server'
import Image from 'next/image'
import { getBaseUrl } from 'utils'
import { TableOfContents } from '@marketing/shared/components/TableOfContents'

type Params = {
  path: string
  locale: string
}

// 智能路径验证函数 - 过滤无效路径
function isValidBlogPath(path: string | string[]): boolean {
  if (Array.isArray(path)) {
    // 检查是否包含文件扩展名
    const hasFileExtension = path.some((segment) =>
      /\.(jpg|jpeg|png|gif|webp|svg|css|js|xml|txt|pdf|doc|docx|xls|xlsx|ppt|pptx|zip|rar|mp3|mp4|avi|mov|ico|woff|woff2|ttf|eot)$/i.test(
        segment
      )
    )

    if (hasFileExtension) {
      return false
    }

    return true
  }

  return isValidBlogPath([path])
}

export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params

  const { path } = params

  // 🔥 重要：在 generateMetadata 中也要验证路径有效性
  if (!isValidBlogPath(path)) {
    return {
      title: 'Blog',
      description: 'Blog posts about wood carving and crafts',
    }
  }

  const locale = await getLocale()
  const activePath = getActivePathFromUrlParam(path)
  const post = allBrandStories.find(
    (post) => post.path === activePath && locale === post.locale
  )

  return {
    title: post?.title,
    description: post?.excerpt,
    keywords: post?.tags,
    openGraph: {
      title: post?.title,
      description: post?.excerpt,
      keywords: post?.tags,
      images: post?.image
        ? [new URL(post?.image ?? '', getBaseUrl()).toString()]
        : [],
    },
  }
}

export default async function BlogPostPage(props: { params: Promise<Params> }) {
  const params = await props.params
  const t = await getTranslations()
  const { path } = params

  // 使用智能路径验证
  if (!isValidBlogPath(path)) {
    const locale = await getLocale()
    redirect({ href: '/brand-story', locale })
  }

  const locale = await getLocale()
  const activePath = getActivePathFromUrlParam(path)

  const post = allBrandStories.find(
    (post) => post.path === activePath && locale === post.locale
  )

  if (!post) {
    return redirect({ href: '/brand-story', locale })
  }

  const { title, date, body, excerpt } = post

  // 目录 toc 直接用 content-collections 自动生成的字段
  const decodeEscaped = (str: string) => {
    str = str.replace(/\\u\{([0-9A-Fa-f]+)\}/g, (_, hex) =>
      String.fromCodePoint(parseInt(hex, 16))
    )
    str = str.replace(/\\u([0-9A-Fa-f]{4})/g, (_, hex) =>
      String.fromCharCode(parseInt(hex, 16))
    )
    return str.replace(/\\x([0-9A-Fa-f]{2})/g, (_, hex) =>
      String.fromCharCode(parseInt(hex, 16))
    )
  }
  const headings = (post.toc || []).map((item) => ({
    ...item,
    slug: decodeEscaped(item.slug),
    content: decodeEscaped(item.content),
  }))

  return (
    <div className="container pt-32 pb-24">
      <div className="mb-12 text-center space-y-1">
        <h1 className="font-bold text-4xl">{title}</h1>
        {/* <p className="text-gray-300">Last Updated: {date}</p> */}
        {/* <p className="mx-auto max-w-2xl text-gray-300">{excerpt}</p> */}
      </div>

      <PostContent content={body} />
    </div>
  )
  // return (
  //   <div className="container max-w-8xl pt-32 pb-24 relative min-[1230px]:pl-[300px]">
  //     {/* 显示目录 - 隐藏在小屏幕上，大屏幕上显示 */}
  //     <div className="hidden min-[1230px]:block">
  //       <TableOfContents headings={headings} />
  //     </div>

  //     <div className="mx-auto max-w-2xl">
  //       <div className="mb-12">
  //         <Link href="/brand-story">&larr; {t('back_to_blog')}</Link>
  //       </div>

  //       <h1 className="font-bold text-4xl">{title}</h1>

  //       <div className="mt-4 flex items-center justify-start gap-6">
  //         {authorName && (
  //           <div className="flex items-center">
  //             {authorImage && (
  //               <div className="relative mr-2 size-8 overflow-hidden rounded-full">
  //                 <Image
  //                   src={authorImage}
  //                   alt={authorName}
  //                   fill
  //                   sizes="96px"
  //                   className="object-cover object-center"
  //                 />
  //               </div>
  //             )}
  //             <div>
  //               <p className="font-semibold text-sm opacity-50">{authorName}</p>
  //             </div>
  //           </div>
  //         )}

  //         <div className="mr-0 ml-auto">
  //           <p className="text-sm opacity-30">
  //             {Intl.DateTimeFormat('en-US').format(new Date(date))}
  //           </p>
  //         </div>

  //         {tags && (
  //           <div className="flex flex-1 flex-wrap gap-2">
  //             {tags.slice(0, 2).map((tag) => (
  //               <span
  //                 key={tag}
  //                 className="font-semibold text-primary text-xs uppercase tracking-wider"
  //               >
  //                 #{tag}
  //               </span>
  //             ))}
  //           </div>
  //         )}
  //       </div>
  //     </div>

  //     {image && (
  //       <div className="relative mt-6 aspect-[16/9] overflow-hidden rounded-xl mx-auto max-w-2xl">
  //         <Image
  //           src={image}
  //           alt={title}
  //           fill
  //           className="object-cover object-center"
  //         />
  //       </div>
  //     )}
  //     <div className="flex justify-center relative">
  //       <div className="max-w-6xl flex-1">
  //         <PostContent content={body} />
  //       </div>
  //     </div>
  //   </div>
  // )
}
