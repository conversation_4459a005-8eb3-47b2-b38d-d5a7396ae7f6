// do well
import { NavBar } from '@marketing/shared/components/NavBar'
import { UserContextProvider } from '@saas/auth/lib/user-context'
import type { PropsWithChildren } from 'react'
import { Toaster } from 'react-hot-toast'
import { Web3Provider } from '@/providers/Web3Provider'
import { Header } from '@marketing/shared/components/Header'
import { Footer } from '@marketing/shared/components/Footer'

export default function MarketingLayout({ children }: PropsWithChildren) {
  return (
    <Web3Provider>
      <UserContextProvider initialUser={null}>
        <Toaster
          position="top-center"
          reverseOrder={false}
          toastOptions={{
            // 默认样式配置
            duration: 2000,
            style: {
              background: '#333',
              color: '#fff',
            },
          }}
        />
        {/* <NavBar /> */}
        <Header />
        <main className="min-h-screen">{children}</main>
        <Footer />
      </UserContextProvider>
    </Web3Provider>
  )
}
