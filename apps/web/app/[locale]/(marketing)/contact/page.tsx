import { Mail } from 'lucide-react'
import { getTranslations } from 'next-intl/server'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('contactPageTitle'),
    description: t('contactPageDesc'),
    keywords: t('contactPageKeywords'),
  }
}
export default async function PricingPage() {
  const t = await getTranslations()

  return (
    <main className="px-8 pt-40 pb-24">
      <section className="py-12 md:py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="max-w-2xl">
            <div className="mb-6">
              <div className="inline-block p-3 rounded-xl bg-blue-500/10 mb-4">
                <Mail className="w-6 h-6 text-blue-400" />
              </div>

              {/* 将 H2 升级为 H1，保持相同的样式 */}
              <h1 className="text-3xl font-bold mb-4 text-purple-500">
                {t('contactSeoTitle')}
              </h1>

              <p className="text-lg text-gray-400 leading-relaxed mb-8">
                {t('contact.description')}
              </p>
            </div>

            <div className="text-sm text-gray-400 leading-relaxed mb-8">
              {t('translation')}
            </div>

            {/* <div className="text-sm text-gray-400 leading-relaxed mb-8">
              RM Al,11/F, WINNER BUILDING, 36 MAN YUE STREET, HUNG HOM HONG KONG
            </div> */}

            <div className="space-x-4 flex items-center">
              <h3 className="text-gray-400">{t('contact.email.label')}</h3>
              <a
                href="mailto:<EMAIL>"
                className="text-lg text-white hover:text-purple-400 transition-colors"
              >
                <EMAIL>
              </a>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
