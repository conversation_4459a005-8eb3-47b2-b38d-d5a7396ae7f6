// apps/web/app/api/schedule/music-tasks/update-status/route.ts

import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'

export async function GET(req: Request) {
  try {
    // 获取URL对象，以便提取查询参数
    const url = new URL(req.url)
    const id = url.searchParams.get('id')
    const status = url.searchParams.get('status')

    // 验证必要参数
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Task ID is required' },
        { status: 400 }
      )
    }

    if (!status || isNaN(Number(status))) {
      return NextResponse.json(
        { success: false, error: 'Valid status is required' },
        { status: 400 }
      )
    }

    // 更新指定ID的任务状态
    const { data, error } = await supabase
      .from('woodcarvings_task_list')
      .update({
        status: status,
        updated_at: new Date().toISOString(), // 同时更新更新时间
      })
      .eq('id', id)
      .select()

    if (error) {
      throw error
    }

    // 检查是否有记录被更新
    if (!data || data.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No task found with the specified ID' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: `Task status updated to ${status} successfully`,
      task: data[0],
    })
  } catch (error) {
    console.error('Error updating music task status:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update task status' },
      { status: 500 }
    )
  }
}
