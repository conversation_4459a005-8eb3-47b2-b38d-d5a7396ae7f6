import generateVerificationEmail from '@shared/components/VerificationEmailTemplate'
import { NextRequest, NextResponse } from 'next/server'
import nodemailer from 'nodemailer'
import { supabase } from '@/lib/supabaseClient'

export async function POST(request: NextRequest) {
  try {
    const { to, subject, text } = await request.json()

    if (!to) {
      return NextResponse.json(
        { code: 1, error: 'Email address is required' },
        { status: 400 }
      )
    }

    // 生成6位数随机验证码
    const verificationCode = Math.floor(
      100000 + Math.random() * 900000
    ).toString()

    // 创建邮件传输器
    const transporter = nodemailer.createTransport({
      host: process.env.MAIL_HOST,
      port: parseInt(process.env.MAIL_PORT || '587'),
      secure: process.env.MAIL_SECURE === 'true',
      auth: {
        user: process.env.MAIL_USER,
        pass: process.env.MAIL_PASS,
      },
    })

    const locale = request.cookies.get('NEXT_LOCALE')?.value || 'en'
    console.log('🚀 ~ locale:', locale)
    // 使用模板生成HTML内容
    const htmlContent = generateVerificationEmail(verificationCode, locale)
    console.log('🚀 ~ htmlContent:', htmlContent)

    // 邮件选项
    const mailOptions = {
      from: `"removeai" <${process.env.MAIL_USER}>`,
      to,
      subject: subject || 'Your removeai Verification Code',
      html: htmlContent,
      text:
        text ||
        `Your verification code is: ${verificationCode}. This code will expire in 10 minutes.`,
    }

    // 发送邮件
    await transporter.sendMail(mailOptions)

    // 计算过期时间 (5分钟后)
    const expires_at = new Date(Date.now() + 10 * 60 * 1000).toISOString()
    // 先删除该邮箱的旧验证码
    await supabase.from('auth_code').delete().eq('email', to)

    // 插入新验证码记录
    const response = await supabase.from('auth_code').insert([
      {
        code: verificationCode,
        email: to, // 存储关联的邮箱地址
        used: 0, // 0表示未使用
        type: 'verification', // 验证码类型
        expires_at, // 过期时间
        created_at: new Date().toISOString(), // 创建时间
        attempts: 0, // 尝试验证次数
      },
    ])
    if (response.error) {
      console.error('Error inserting into database:', response.error)
      return NextResponse.json(
        { code: 1, error: 'Failed to insert verification code into database' },
        { status: 500 }
      )
    }
    console.log('Verification code inserted into database:', response)
    return NextResponse.json({
      code: 0,
      message: 'Verification email sent successfully',
    })
  } catch (error) {
    console.error('Error sending email:', error)
    return NextResponse.json(
      { code: 1, error: 'Failed to send email' },
      { status: 500 }
    )
  }
}
