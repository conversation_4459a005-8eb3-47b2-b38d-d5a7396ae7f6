// app/api/cron/expire-memberships/route.ts
import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'

export async function GET(req: Request) {
  try {
    // 验证是否是定时任务调用
    const authHeader = req.headers.get('authorization')
    if (authHeader !== `Bearer ${process.env.CRON_SECRET_KEY}`) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const now = new Date().toISOString()

    // 1. 查询已过期的会员
    const { data: expiredUsers, error: queryError } = await supabase
      .from('woodcarvings_user')
      .select('*')
      .lt('membership_end_date', now) // 结束时间小于当前时间
      .eq('membership_status', 'active') // 当前状态为激活

    if (queryError) {
      console.error('Error querying expired users:', queryError)
      return NextResponse.json(
        { success: false, message: 'Failed to query expired users' },
        { status: 500 }
      )
    }

    if (!expiredUsers || expiredUsers.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No expired memberships found',
      })
    }

    // 2. 批量更新操作
    const updatePromises = expiredUsers.map(async (user) => {
      // 更新用户状态和积分
      const { error: updateError } = await supabase
        .from('woodcarvings_user')
        .update({
          points: 0, // 积分清零
          membership_status: 'expired', // 设置为过期状态
          updated_at: now,
        })
        .eq('id', user.id)

      // 记录会员变更日志
      if (!updateError) {
        await supabase.from('woodcarvings_membership_log').insert({
          user_id: user.id,
          old_status: 'active',
          new_status: 'expired',
          old_level: user.membership_level,
          new_level: user.membership_level,
          change_reason: 'membership_expired',
          note: 'Automatically expired due to end date reached',
        })
      }

      return { userId: user.id, error: updateError }
    })

    // 3. 等待所有更新完成
    const results = await Promise.all(updatePromises)

    // 4. 统计处理结果
    const failures = results.filter((r) => r.error)
    const successes = results.filter((r) => !r.error)

    return NextResponse.json({
      success: true,
      message: 'Membership expiration process completed',
      stats: {
        total: expiredUsers.length,
        succeeded: successes.length,
        failed: failures.length,
      },
      failures: failures.length > 0 ? failures : undefined,
    })
  } catch (error) {
    console.error('Error processing expired memberships:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    )
  }
}
