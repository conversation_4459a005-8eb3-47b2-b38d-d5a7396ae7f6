// app/api/webhooks/payssion/route.ts
import { supabase } from '@/lib/supabaseClient'
import { NextResponse } from 'next/server'

export async function POST(req: Request) {
  try {
    // 1. 解析请求数据
    const data = await req.json()
    console.log('Payssion webhook data:', data)

    // 2. 基本验证
    if (!data.order_id || !data.state) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      )
    }

    // 3. 检查状态 - 只处理成功的支付
    if (data.state !== 'completed') {
      console.log(`Payment not completed, current state: ${data.state}`)

      // 如果状态是失败或取消，可以更新订单状态
      if (data.state === 'failed' || data.state === 'cancelled') {
        const { data: existingOrder } = await supabase
          .from('woodcarvings_order')
          .select('id')
          .eq('order_number', data.order_id)
          .single()

        if (existingOrder) {
          await supabase
            .from('woodcarvings_order')
            .update({
              status: data.state,
              updated_at: new Date().toISOString(),
            })
            .eq('id', existingOrder.id)
        }
      }

      return NextResponse.json(
        { message: `Payment state: ${data.state}`, success: true },
        { status: 200 }
      )
    }

    // 4. 查找订单记录
    const { data: existingOrder, error: orderQueryError } = await supabase
      .from('woodcarvings_order')
      .select('id, user_id, product_id, status')
      .eq('order_number', data.order_id)
      .single()

    if (orderQueryError || !existingOrder) {
      console.error('Order not found:', data.order_id)

      return NextResponse.json(
        { error: `Order not found: ${data.order_id}` },
        { status: 200 } // 返回200以避免Payssion不断重试
      )
    }

    // 如果订单已经处理，避免重复处理
    if (existingOrder.status === 'paid') {
      console.log('Order already processed:', data.order_id)
      return NextResponse.json(
        { message: 'Order already processed', success: true },
        { status: 200 }
      )
    }

    // 5. 获取用户信息
    const { data: user, error: userError } = await supabase
      .from('woodcarvings_user')
      .select('id, membership_status, membership_level')
      .eq('id', existingOrder.user_id)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: `User not found with ID ${existingOrder.user_id}` },
        { status: 200 } // 返回200以避免Payssion不断重试
      )
    }

    // 6. 更新订单状态
    await supabase
      .from('woodcarvings_order')
      .update({
        status: 'paid',
        payment_method: data.pm_id || 'payssion',
        subscription_id: data.transaction_id || '',
        amount: data.amount || 0,
        currency: data.currency || 'USD',
        paypro_order_id: data.transaction_id || '',
        updated_at: new Date().toISOString(),
      })
      .eq('id', existingOrder.id)

    // 7. 获取会员等级信息
    // 首先尝试从product_id获取会员等级
    let { data: membershipLevel, error: membershipError } = await supabase
      .from('woodcarvings_membership_level')
      .select('*')
      .eq('id', existingOrder.product_id)
      .single()

    // 如果找不到，尝试通过金额匹配
    if (membershipError || !membershipLevel) {
      const { data: membershipByPrice, error: membershipByPriceError } =
        await supabase
          .from('woodcarvings_membership_level')
          .select('*')
          .eq('price', data.amount)
          .eq('currency', data.currency)
          .single()

      if (membershipByPriceError || !membershipByPrice) {
        // 如果还是找不到，使用默认的基础会员（ID为1）
        const { data: basicMembership } = await supabase
          .from('woodcarvings_membership_level')
          .select('*')
          .eq('id', 1)
          .single()

        if (!basicMembership) {
          return NextResponse.json(
            { error: 'Could not determine membership level' },
            { status: 200 }
          )
        }

        membershipLevel = basicMembership
      } else {
        membershipLevel = membershipByPrice
      }
    }

    // 8. 更新用户会员状态
    const membershipEndDate = new Date()
    membershipEndDate.setDate(
      membershipEndDate.getDate() + membershipLevel.duration_days
    )
    // 先查询用户现有的积分，累加
    const { data: userPoints } = await supabase
      .from('woodcarvings_user')
      .select('points')
      .eq('id', user.id)
      .single()
    console.log('userPoints -->', userPoints)
    const newPoints =
      parseInt(userPoints?.points || 0) + parseInt(membershipLevel.points || 0)
    // 更新用户积分
    await supabase
      .from('woodcarvings_user')
      .update({
        membership_status: 'active',
        membership_level: membershipLevel.name,
        membership_start_date: new Date().toISOString(),
        membership_end_date: membershipEndDate.toISOString(),
        updated_at: new Date().toISOString(),
        points: newPoints,
      })
      .eq('id', user.id)

    // 9. 记录会员变更
    await supabase.from('woodcarvings_membership_log').insert({
      user_id: user.id,
      order_id: existingOrder.id,
      old_status: user.membership_status || 'free',
      new_status: 'active',
      old_level: user.membership_level || 'free',
      new_level: membershipLevel.name,
      start_date: new Date().toISOString(),
      end_date: membershipEndDate.toISOString(),
      change_reason: 'payssion_purchase',
      transaction_id: data.transaction_id || '',
    })

    // 返回成功响应
    return NextResponse.json({
      orderId: existingOrder.id,
      transactionId: data.transaction_id || data.order_id,
      success: true,
    })
  } catch (error: any) {
    console.error('Error processing Payssion webhook:', error)

    // 记录错误但返回200，避免Payssion不断重试
    return NextResponse.json(
      {
        success: false,
        error: 'Error processing payment but acknowledged',
      },
      { status: 200 }
    )
  }
}
