import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { supabase } from '../../../lib/supabaseClient'

export async function POST(req: Request) {
  try {
    const { reasonText } = await req.json()

    // 获取当前用户session
    const cookieStore = await cookies()
    const oauthEmail = cookieStore.get('oauth_email')

    if (!oauthEmail) {
      return new Response('Unauthorized', { status: 401 })
    }

    // 1. 获取订阅信息 - 修改为关联查询
    const { data: subscriptionData, error: subError } = await supabase
      .from('woodcarvings_user')
      .select(
        `
          *,
          woodcarvings_order(
            subscription_id,
            created_at
          )
        `
      )
      .eq('email', oauthEmail.value)
      .not('woodcarvings_order.subscription_id', 'is', null)
      .limit(1)
      .single()

    if (subError || !subscriptionData) {
      return NextResponse.json(
        { success: false, message: 'Subscription not found' },
        { status: 404 }
      )
    }

    // 从关联数据中获取 subscription_id
    const subscriptionId =
      subscriptionData.woodcarvings_order[0]?.subscription_id

    if (!subscriptionId) {
      return NextResponse.json(
        { success: false, message: 'No valid subscription ID found' },
        { status: 404 }
      )
    }

    // 2. 验证订阅状态是否为suspended
    if (subscriptionData.membership_status !== 'suspended') {
      return NextResponse.json(
        {
          success: false,
          message: 'Only suspended subscriptions can be renewed',
        },
        { status: 400 }
      )
    }

    /**
     * 这后面的逻辑先不用了，
     * 直接返回订阅url，通过wbhook进行续订
     * https://store.payproglobal.com/checkout?subscr-id=3903123
     */

    // 构建续订url
    const checkoutUrl = `https://store.payproglobal.com/checkout?subscr-id=${subscriptionId}`
    return new Response(
      JSON.stringify({
        checkoutUrl: checkoutUrl,
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      }
    )

    // 3. 调用 PayPro Global API 续订订阅
    const payproResponse = await fetch(
      'https://store.payproglobal.com/api/Subscriptions/Renew',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionId: subscriptionId,
          reasonText: reasonText || 'Customer requested renewal',
          vendorAccountId: 169067,
          apiSecretKey: '779196d6-3d8d-44cf-b128-227b6a4eb793',
        }),
      }
    )

    const payproData = await payproResponse.json()

    if (!payproData.isSuccess) {
      return NextResponse.json(
        {
          success: false,
          message: 'Failed to renew subscription',
          errors: payproData.errors,
        },
        { status: 400 }
      )
    }

    // 获取会员等级对应的积分
    const { data: membershipLevelData, error: membershipLevelError } =
      await supabase
        .from('woodcarvings_membership_level')
        .select('points')
        .eq('description', subscriptionData.membership_level)
        .single()

    if (membershipLevelError) {
      console.error(
        'Error fetching membership level points:',
        membershipLevelError
      )
      return NextResponse.json({
        success: false,
        message: 'Failed to fetch membership level points',
      })
    }

    // 4. 更新本地数据库中的订阅状态和积分
    const { error: updateError } = await supabase
      .from('woodcarvings_user')
      .update({
        membership_status: 'active',
        points: membershipLevelData.points, // 重置积分
        updated_at: new Date().toISOString(),
      })
      .eq('id', subscriptionData.id)

    if (updateError) {
      console.error('Error updating subscription status:', updateError)
      return NextResponse.json({
        success: true,
        message: 'Subscription renewed but local update failed',
      })
    }

    // 5. 记录会员变更日志
    await supabase.from('woodcarvings_membership_log').insert({
      user_id: subscriptionData.id,
      old_status: 'suspended',
      new_status: 'active',
      old_level: subscriptionData.membership_level,
      new_level: subscriptionData.membership_level,
      change_reason: 'subscription_renewed',
      note: reasonText,
    })

    return NextResponse.json({
      success: true,
      message: 'Subscription renewed successfully',
    })
  } catch (error) {
    console.error('Error renewing subscription:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    )
  }
}
