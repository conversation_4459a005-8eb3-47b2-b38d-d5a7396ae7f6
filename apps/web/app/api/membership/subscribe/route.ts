// apps/web/app/api/membership/subscribe/route.ts
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'
import { supabase } from '../../../lib/supabaseClient'
import crypto from 'crypto'
//

// 生成Payssion签名的函数
function generateSignature(orderData: any, secretKey: string): string {
  const fields = [
    orderData.api_key,
    orderData.pm_id,
    orderData.amount,
    orderData.currency,
    orderData.order_id,
    secretKey,
  ]

  // 创建一个连接字符串
  const concatenatedString = fields.join('|')

  // 生成MD5哈希
  return crypto.createHash('md5').update(concatenatedString).digest('hex')
}

/**
 * 创建RSA-SHA256签名
 * @param {string} requestBody - 请求体字符串
 * @param {string} privateKey - 私钥字符串（不包含PEM头尾）
 * @returns {string} Base64编码的签名
 */
function createSignature(requestBody: string, privateKey: string): string {
  try {
    // 将私钥转换为PEM格式
    const pemKey = `-----BEGIN PRIVATE KEY-----\n${privateKey}\n-----END PRIVATE KEY-----`

    // 创建签名对象
    const sign = crypto.createSign('RSA-SHA256')

    // 更新要签名的数据
    sign.update(Buffer.from(requestBody, 'utf8'))

    // 生成签名并返回Base64编码
    return sign.sign(pemKey, 'base64')
  } catch (error) {
    throw new Error(
      `签名创建失败: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    )
  }
}

// 生成Checus签名的函数
function generateChecusSignature(data: any, privateKey: string): string {
  const dataString = JSON.stringify(data)
  return createSignature(dataString, privateKey)
}

export async function POST(req: Request) {
  try {
    const {
      membershipLevelId,
      isYearly,
      paymentMethod = 'paypro',
      paymentChannel,
      isMobile = false,
    } = await req.json()
    // 0 免费
    // 1 基础 isYearly => 4
    // 2 高级 isYearly => 5
    // 3 无限 isYearly => 6

    const mappedMembershipLevelId = isYearly
      ? membershipLevelId === 1
        ? 4 // 基础年付 => 4
        : membershipLevelId === 2
        ? 5 // 高级年付 => 5
        : membershipLevelId === 3
        ? 6 // 无限年付 => 6
        : membershipLevelId // 免费或其他不变
      : membershipLevelId // 月付不变

    // 获取当前用户session
    const cookieStore = await cookies()
    console.log('cookieStore :>> ', cookieStore)
    const oauthEmail = cookieStore.get('oauth_email')

    console.log('oauthEmail :>> ', oauthEmail)
    if (!oauthEmail) {
      return new Response('Unauthorized', { status: 401 })
    }

    // 验证会员等级
    const { data: membershipLevel, error: membershipError } = await supabase
      .from('woodcarvings_membership_level')
      .select('*')
      .eq('id', mappedMembershipLevelId)
      .single()

    console.log('faith=============membershipLevel', isYearly, membershipLevel)

    if (membershipError || !membershipLevel) {
      return new Response('Invalid membership level', { status: 400 })
    }

    // 根据邮箱获取用户id
    const { data: user, error: userError } = await supabase
      .from('woodcarvings_user')
      .select('id')
      .eq('email', oauthEmail.value)
      .single()

    if (userError || !user) {
      return new Response('User not found', { status: 404 })
    }
    console.log('user :>> ', user)

    // 创建订单 ORD+时间戳+随机数+平台标识
    const orderNumber = `ORD${Date.now()}${Math.floor(Math.random() * 1000)}-${
      isMobile ? 'M' : 'P'
    }`
    console.log('orderNumber :>> ', orderNumber)
    const { data: order, error: orderError } = await supabase
      .from('woodcarvings_order')
      .insert({
        user_id: user.id,
        order_number: orderNumber, // 保持原有的订单号生成方式
        product_id:
          paymentMethod === 'paypro'
            ? membershipLevel.paypro_product_id
            : membershipLevel.id.toString(),
        amount: membershipLevel.price,
        currency: membershipLevel.currency,
        status: 'pending',
        payment_method: paymentMethod, // 记录支付方式
        paypro_order_id: null, // 新增字段，用于存储 PayPro 的订单ID
      })
      .select()
      .single()

    if (orderError) {
      console.log('orderError :>> ', orderError)
      return new Response('Failed to create order', { status: 500 })
    }

    // 根据不同的支付方式返回不同的响应
    if (paymentMethod === 'paypro') {
      // 原有的 PayPro 支付逻辑
      const checkoutUrl = `https://store.payproglobal.com/checkout?products[1][id]=${membershipLevel.paypro_product_id}&internal_order_id=${order.id}&user_id=${user.id}&membership_level_id=${membershipLevel.id}`
      return new Response(
        JSON.stringify({
          orderId: order.id,
          orderNumber: orderNumber,
          checkoutUrl: checkoutUrl,
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        }
      )
    } else if (paymentMethod === 'other') {
      // Payssion 支付逻辑
      // 准备Payssion支付数据
      const orderData = {
        // api_key: 'sandbox_902916da98f5a156', // 沙箱环境API密钥
        // pm_id: 'payssion_test', // 沙箱环境支付方式ID
        api_key: 'live_797b9ec13fc21e9c',
        pm_id: paymentChannel,
        order_id: orderNumber, // 使用生成的订单号
        description: `${membershipLevel.name} `,
        amount: membershipLevel.price.toString(),
        currency:
          membershipLevel.currency === 'dollar'
            ? 'USD'
            : membershipLevel.currency,
        // return_url: 'http://sandbox.payssion.com/demo/afterpayment',
      }

      // 生成签名
      const secretKey = 'pFu6wDiBeWuWrJyB9YNS0gKYorfgpo3P'
      // const secretKey = '********************************' // 沙箱环境密钥
      const api_sig = generateSignature(orderData, secretKey)

      return new Response(
        JSON.stringify({
          ...orderData,
          api_sig,
          orderId: order.id,
          order_id: orderNumber,
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        }
      )
    } else if (paymentMethod === 'checus') {
      const checusData = {
        version: '1.4',
        keyVersion: '1',
        requestTime: new Date().toISOString(),
        appId: process.env.CHECUS_APP_ID || '06b680acaede4276b6a2e9e0a84d5ec6',
        merchantNo: process.env.CHECUS_MERCHANT_NO || 'SDP07010117493826',
        data: {
          outTradeNo: orderNumber,
          integrate: 'Hosted_Checkout',
          subject: `${membershipLevel.name} Subscription`,
          totalAmount: membershipLevel.price,
          currency:
            membershipLevel.currency === 'dollar'
              ? 'USD'
              : membershipLevel.currency,
          country: 'RU',
          userId: oauthEmail.value,
          frontCallbackUrl: `https://www.woodcarvings.art/auth/login`,
          notifyUrl: `https://www.woodcarvings.art/api/webhooks/checus`,
          // frontCallbackUrl: `http://*************:3000/auth/login`,
          // notifyUrl: `http://*************:3000/api/webhooks/checus`,
        },
      }

      // 生成 Checus 签名
      const checusPrivateKey =
        process.env.CHECUS_PRIVATE_KEY ||
        'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCI9TWT5htZJX9wNUxQxp2GgaZYpXwS9QVOOYVxA15RGgSiMhopSaeCaLKBq2aGEGO5rMfVhJpiBBZcAngU3ExnIah/+0NkFLm7X4VijA+ttq/drzxrpJvBvq7BjagxR+o3QD5zY0pjAUD/jqN/15JAfXOjwwbVBm0w9IMqqf+XBJYAECaP+I2HOAIjT0s6/OUbCGX9wIhiHBZJsDKDW01KS4gxCtXp8rpf/R46dLGwgb6KPEiHebtBJY0ZFL1NhKJO2scNvxwUwOMuBtbB0jRivHZJ/PPQwLZBzZMPtLYm21S0L5GRpHg1XLsvSdv/ksM3QFRvkpkMkqNSPB16IzsjAgMBAAECggEAF/4lEbDZD6fzCFw971SsGfkyhAsRjlygPlDzYJX/euO89hEi5iLCFqVyyCaXwlqyrb9oDq65QPyIUjwYf0cMl3CdDTaeWrUKyJl2S4pP4NXFP2fIhO8EXk+iW8QON/vqOx2lEx/M00VP9rUtTWMdevC/RmwPEVib3lyncdzwKqtekuYfRhzvShDQTnAvDaLqAed3iQ6koEkDzlmRVZ+G2/RmeqmHOKalp3b9qLXQJonA1xCwYajth98EX/j2YbVi3IgUGappfQom2lczKoBDPmO/mlm6VxPW+/bZeZoPyj8SIT16xCBbD3AR/LqBCp3xmK42IzH26F4tdgGDlvUuaQKBgQDs15X4Nmje3AwaGMKE2rowPsUJ3cB6SRgzQlHDE019D8fi2JgEZE4xZLpzvQEAwDDUQdm43LU4ZKTD+oLtq1jat0P4syuRSohTp7RwNZPql1GSHAwOXnwJD9y8mC9K0MqkVz/6zhAL6SqyguIqZM/IGT7yrnYjW/LSjJFLVJxErwKBgQCUCURq3E90t4WrL2WnXSlzfNF/ejwtP8yFA3VdSHqGuX5w2vTzUkWP9UrkHS6jZW0AeRf82eXorVp+tlH8tFlMgrhCxp6e5HN5PQF+QcgduEOUpC2qGB1Xjc+gbfGIOibMw2zEo5/bi6bdSV62geCy8qm2GPY9OYfCljItT7g1zQKBgQDBD2NSN5f4wkqjIze88EcBeMlJJ5f0lozBi/f2Gk/599kqUwBn5BPRM5jBK3ob0I59F0iQ5mhalb+xeQ0tC5Wcg/NGUVbcUnMaLgRpy2LNwZdekLpLJD3bbTC0EyHJzLTCjdS7bympa+HALeyKPi8Zn2qujAQFArjHZIoza78ORQKBgHwc8PGu2CcBHt6RFGcnYnNi+LoqH33IeY4jgii9dYyyxGGWEbxXA99YRnvOQ6XWiXbWD9h8T5euj6qg7UgzZJMTHa1OnPqWioHhNflG6aKRNIfQxkBPi3o2eRcGVnRiXNlcFzsd/A6znSj2NKRLa7pcXRFBCGH4c+wexN7ccZMZAoGAWQa8njHgcgMx+TNBp6d4S5wXDX8xEniufF+OZS2p2LOlLIelZWNAQYqheis4ZkeL5cXNmux7do8rGNqAh1UCsWDRuFaB+gYqgKbgBo02BY3IXjG5D6YsoyPnCXbuTx5GC3STZoa4ku8aZkd7rWlnb+mCkMWOdxFPYNu0i2cNYtg='
      const sign = generateChecusSignature(checusData, checusPrivateKey)

      return new Response(
        JSON.stringify({
          orderId: order.id,
          orderNumber: orderNumber,
          sign,
          checusData,
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        }
      )
    } else {
      return new Response('Unsupported payment method', { status: 400 })
    }
  } catch (error) {
    console.error('Subscription error:', error)
    return new Response(
      `Subscription error: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`,
      { status: 400 }
    )
  }
}

export async function GET() {
  try {
    const { data: levels, error } = await supabase
      .from('woodcarvings_membership_level')
      .select('*')
      .eq('is_active', true)
      .order('price', { ascending: true })

    if (error) {
      throw error
    }

    return new Response(JSON.stringify(levels), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('Error fetching membership levels:', error)
    return new Response(
      `Error fetching membership levels: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`,
      { status: 400 }
    )
  }
}
