export const Google_One_Tap_Options = {
  client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID, // required
  auto_select: false, // optional
  cancel_on_tap_outside: false, // optional
  context: 'signin', // optional
  // Google One Tap使用JWT token模式，不需要login_uri和ux_mode
  itp_support: true,
  // 添加这些配置来解决CORS问题
  use_fedcm_for_prompt: false, // 禁用FedCM API
}

export const googleOneTap = (
  {
    client_id,
    auto_select = false,
    cancel_on_tap_outside = false,
    context = 'signin',
    ...otherOptions
  }: any,
  // eslint-disable-next-line no-unused-vars
  callback: (res: any) => any
) => {
  if (!client_id) {
    throw new Error('client_id is required')
  }

  // 检查是否在本地开发环境且使用代理
  if (typeof window !== 'undefined') {
    const isLocalhost =
      window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1'
    const hasProxy =
      window.location.port === '7890' || navigator.userAgent.includes('proxy')

    if (isLocalhost && hasProxy) {
      console.warn(
        'Google One Tap may not work properly with proxy servers. Consider disabling proxy for localhost.'
      )
    }
  }

  if (typeof window !== 'undefined' && window.document) {
    const contextValue = ['signin', 'signup', 'use'].includes(context)
      ? context
      : 'signin'

    const initializeGoogleOneTap = () => {
      // @ts-ignore
      if (window && window?.google && window.google.accounts) {
        try {
          // @ts-ignore
          window.google.accounts.id.initialize({
            client_id: client_id,
            callback: callback,
            auto_select: auto_select,
            cancel_on_tap_outside: cancel_on_tap_outside,
            context: contextValue,
            ...otherOptions,
          })

          // 延迟显示提示，确保页面完全加载
          setTimeout(() => {
            try {
              // @ts-ignore
              window.google.accounts.id.prompt((notification: any) => {
                if (
                  notification.isNotDisplayed() ||
                  notification.isSkippedMoment()
                ) {
                  console.log(
                    'Google One Tap was not displayed:',
                    notification.getNotDisplayedReason()
                  )
                }
              })
            } catch (promptErr) {
              console.error('Error showing Google One Tap prompt:', promptErr)
            }
          }, 1000)
        } catch (err) {
          console.error('Error initializing Google One Tap:', err)
        }
      } else {
        console.error('Google GSI client not available')
      }
    }

    // 检查是否已经加载了Google脚本
    const existingScript = document.querySelector(
      'script[src="https://accounts.google.com/gsi/client"]'
    )
    if (existingScript) {
      // 如果脚本已存在，直接初始化
      initializeGoogleOneTap()
      return
    }

    // 创建并加载Google脚本
    const googleScript = document.createElement('script')
    googleScript.src = 'https://accounts.google.com/gsi/client'
    googleScript.async = true
    googleScript.defer = true

    googleScript.onload = initializeGoogleOneTap
    googleScript.onerror = function () {
      console.error('Failed to load Google GSI client script')
    }

    document.head.appendChild(googleScript)
  }
}
