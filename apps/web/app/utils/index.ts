export const encryptVerification = async (prompt: string) => {
  try {
    // 获取前20个字符
    const prefix = prompt.substring(0, 20)

    // 加盐
    const salt = 'removeai2025'
    const saltedText = prefix + salt

    // 检查环境
    if (typeof window !== 'undefined') {
      // 浏览器环境
      const encoder = new TextEncoder()
      const data = encoder.encode(saltedText)
      const key = await crypto.subtle.importKey(
        'raw',
        encoder.encode(salt),
        { name: 'HMAC', hash: 'SHA-256' },
        false,
        ['sign']
      )
      const signature = await crypto.subtle.sign('HMAC', key, data)
      const bytes = new Uint8Array(signature)
      let binary = ''
      for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i])
      }
      return btoa(binary)
    } else {
      // Node.js 环境
      const crypto = require('node:crypto')
      const hmac = crypto.createHmac('sha256', salt)
      hmac.update(saltedText)
      return hmac.digest('base64')
    }
  } catch (error) {
    console.error('Encryption error:', error)
    return ''
  }
}
