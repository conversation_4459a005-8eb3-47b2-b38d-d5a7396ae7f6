'use client'

import { motion } from 'framer-motion'

const mediaReports = [
  {
    publication: 'Artisanal Home Magazine',
    quote:
      "<PERSON><PERSON><PERSON><PERSON> is reviving the charm of classic wood carved animals with a modern, soulful twist that's perfect for contemporary homes.",
    logo: 'data:image/jpeg;base64,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',
    logoClassName: 'px-2',
  },
  {
    publication: 'The Woodcraft Journal',
    quote:
      'Their collection of hand carved wooden animals showcases exceptional skill and a deep respect for the material, a true gem for collectors.',
    logo: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQsJCYxJx8fLT0tMTU3Ojo6Iys/RD84QzQ5OjcBCgoKDQwNGg8PGjclHyU3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3N//AABEIAMAAzAMBIgACEQEDEQH/xAAcAAACAgMBAQAAAAAAAAAAAAAABgQFAgMHAQj/xABIEAACAQMDAQUFBAYIBAQHAAABAgMABBEFEiExBhNBUWEUIjJxgQcjkaEVQrHB4fAkM1JicnOy0TRUkvElQ1N0FjVERVWiwv/EABkBAAMBAQEAAAAAAAAAAAAAAAIDBAABBf/EAC4RAAICAQQBAQYGAwEAAAAAAAABAhEDBBIhMUEyEyIzUWHwFFJxgZGhscHRQv/aAAwDAQACEQMRAD8A7jRRRWMYv8DfKvlm/wD+Puv/AHEn+o19TP8AA3yr5Zv/APj7r/3En+o0jN4KMHbNFFFTNHlgg1S3kuwDAu/eD/gYD8yKnKCHRTBDJoD2lo1xDsMUe2aPvWL43OfdKhQScr1HSvYm7PwXsSqolheORJnk3cYwqlfIthn9NwHhXdpzcLp460FgOpApq0m40azvZj3kELm3+7l7szRxv3hPG/d7xj2jPQHOMdKsdT1nR4dLZNGltkmSUuoa2XLDeG6FMdM+I+tS5NRKE9qidETIxnIxRuXOMjPzp5tNb0p+0moT3DWi2TKiWrG2UbF43bfdIz14YHNY2evaUsNqLmSHu7Z5mWP2NAZAOIQcDyOSKB6ma/8AD+19owkbgTgEZrzen9pfxp+Osdnre1ube1MbiSSeaEyW6sELAMgIK54bIGCMYrK37RabJLOs0tuoNpB3bG2RQJcfedEPp1GPLFC9Vk72MwgAgjIIxXp468U46xqejTWCor2s7A23dRra7TEVb70lwASGHGM/hUOYdnA7yhY5O8lXbGryKEUuocgAjGFLkD0HhxVWCTyxt8HG6Frxx40UwiTs8bSESwjeT94qyS/dfFkrluT8HXI9K23FroVpeIt1EhhbvX+4lkJKhhsByx5YE58OPCnUaxZoPBwetMGzQNjLEYztjKRtK8nvyhiNzDPEZXa3GCCSM+FYai2kRadcxad3JaQpjJdn3ByW+I4C424I58ya5RtxRUDqKKB8QFCEdb+wn/gdW/z0/wBNdTFct+wn/gNW/wA9P9NdSFW4/SiLL62e0UUUYsKKKKxjGT4G+VfLV6p/SNyMqM3EmSfD3jX1K/wN8q+XL+Njf3Y2gn2mT4W8NxqfP4H4PJP1Ts7dadp8d9PPZm3l/qyk+TJ/hx1qvSwuG05tQ7s+zLL3RfybGcUzWxbU/s+v7UKTcaXKLhA3XYev5bj9KstGigudFn7I7At5JYC7HHJlJzj6Db/1V471WSEXu5adP9Pn/BSKen6JPfWM99FcWaQQf1nfTBSvlkeGaF0O4jiSS8mt7JZOU9pk2sw8wvXFMXY+0jki0mxnUFb+5a5kRx8SRjCj/qz+FLfaCeS41m/nuTmZ5XGM8AKSAv06U2GaU8rxp9ff+mYkP2X1AQ+1K1q9ljPtYnBi645PgfSsV7OXU8TyWE1rfGMbnjtpQXUee08n6U/20YtdStezYiDaZLo5d128NJk5bPngfnSBpkN5pmrRXlu4DQSY3Z6jOCPLB/fSMGpyZd1eOvqjGOmaBc6laT3NvPaBLZd84lmCmJfMjw6H8Kyi7OXdyG9gns7xlGWS3uFdvoOtPl/p0EOodqjBhVutMDFR4Eq+TikCCD2OaO6t2cSQsJEZfMc0WDVTzKTi66/tGfBXJBJJOLdYXacttEW33t3ljzqxbRGifurvULG2n8YpJssvoccD610rUdPtrbV9W1iKNRdjT1cEDo7ZBb54ArmQsY3+IvknBJ5J8/HzosGr/Ecx4Sr+TWSI+zF8+qfo1ZbX2kxrIgMwxKpGcqfGvB2bv39oW1e1upIM97HBOGdcHB93rVv2RjZu0+lmSUt3PuKG8FCtgD8am6Tpk+i65e9obpg9rFJKAkThjIzkhVODwOeSfSgyamcJONq0lX1ZrEIjCknG0DJJqVqOmz6XOkN3H3bvEsoHHRh+6rDRtLnv9dj7+DfCJGuJ0j53IvvFAPU4X61edpoL7U+y+n6rqNu8V/bSNDcb12+6TkNjy/jVE9TtnGPh9/v0dXJQw9nbqTTBqXtNitpuCGR7gKFbj3T5HkcV6nZq9lEPcz2ciTByJUuFKrsALZPhgEVb6bClx9nl4jXVvCDqKHvLhyqggLxwDVNqMEukWFqtvfpLHeJJJN7PJvjJ3bMA4B+EAGlxzTnJx3c210cPV7OXMlnNeJf6cbaFwkkoul2qxxgE+fIrVcaHdW0KTmW0kheNpFeKYMGCnBwfP0qxtDj7ONccED+nW/HiPejqgjmlkiS3aYrGuWROoyeTgU3HLJNy56df0Y6v9hP/AAOrf5yf6a6lXLvsLObDVc7v65OWGM+7XUa9XH6URZPUwoooowAooorGMJPgb5V8yX0TC8vGESM3tD/1b54y3h+FfTknwGvm++hzc3TC1diLhv6o5yNx54zUup8FOnV2SeyGqwaZrWdQPdWN1G8E4kPABGR+Yx9ag6drs0HaYa3IsgBuC7RYyVjJxt48l4+leW91p9s836QsTdJK0ahJG2tEvvbyD58qR6rUuKTs37LEWs7sTY97bLwrY+fTP5VJ+GxycpPyqGOVM265rqp2rGoaYyd1asFtmX4HUZLY9CS34it+qWml61dNqtldw2rTnvJ7S7OzY/iVbGCD1xx+6ohl0cXFtLb2jgAYmhlAZHOzGc567hmpXtnZ/dIFtLgpjauXxggjkfnx86FaSK27HVKv2BllaGSDWbVNLiikvLdNTW3aCC6IcxbSRwWx148iOvJ5pYtobe1ukudQvLeWKJtxhtXM0kpHRc4AUZ8Tz14qSW0C5ZmS2vy0ewKBIen63JOP7PHrmtButAi1FZTbXPdIjAxYzk+DZz4ftx4UOLQRx3tl32KeZsttO1Nbv9O3Oq6ja2suoW5ihh98mLggZwvTkdM1Q2Vvp9jci41HUheCJwwtrVJD3hHIDOygAfLNbBqOjSKok0+Vdibe8hkI7zgZz4Z+LnwyK2+2dnDch1s7jIZRsJ4IHXxzk0cNFGF1Lh/p4BeRszsO0836fu77UUL294ndzQp+omMAL6j86rp9GimlPsGtWklueVMsjxSKPIqRkkemalabb2F92mhihgc2cjD7uQkYG3LZPlkGpO3SP0Vql0tuBDHcIkEgPvEkkkL5DApUtmDLUFXCXH9fudTbRs0WTS9M16wkGpRey2iffTSBwZnIbJUbc4GQOccCtdnrUGmaxeRXUkN5pN+ziXuc8AkkNyByPH99eXun2T3aGSBYI7PTVubwI2CzHouT4ncoz8622umWl9b6NDe2sdvLdSTPtiBVnjVSVXJPViOtTuOKXvTt2q/y7/XyFcjFpdOstLvls9XgmklcbcrIGMQ5xyvxE449OtbOz9zYzWepWWpXkUMF3FtG4sTvHRhgEY+tare1tLnS4xdWcFpcQxy3FzGilXjhXG0YJ91mPHNR7loI+zlpd3GnxJ390doiyG7pRnr6njNZwjKO23d98ffgJTdmyH2W27H3uky6rYm7e6WZVPe7Ng2jlinB908Ut31vcWUyxXQMe6MOm5sllbkEDwz9KZLz2W5FvanS7Vbx51WRY9wMKk8Kefi6ZHh061saDSJLvtBJ3CLb2abInAz3bcIu0eZOTTMeX2bbafPL6+i4HXZBsjp6djNT0qTVrKO8u545kUiQ4ClDhjs4Pu+vhVFBG2dqSbT0BUZz6j0pmOmWNzaWEndR26w2Ut9dNux3i7gsasx6E9c+WaiXDWkyWqWncwzY2TTRRnbKxPu4zzx5+NMwzVvb5d/p90dZ0H7FQos9SUFjiRBlvHiulCudfY6way1DDs5WRQWPyroor1sLuCI8yqbPaKKKaLCiiisYxf4TXzndwoby5kaKYFLh+nvfrGvouT4D8q+eLmHN5cuqzJtnbHjnkn08ql1Pgr0y7KjU/vJIQHY/4jyoqPHBgnEPw9MNUzVyZbiIENIMY64IryCFNoBgk5HhJ/CkXwOo8ijdZQxgdenvbuKyS1vcOJIgFLe7hh0zW1LfDqwhdcsOTJn8sVLhjnlZgsxADEdKFN3whOWPVmp4rxIRFaxBcj3pC3JqMLG9yfukY9cFv586tWsLtWKi4PAzyKr54rhreMd8/eGbBYHHGKO5IVGEZOka/YrpgS8MS8cYA5rUbG9DZEfHj745rfJY3aAf0yfnn46FtbrGfbZR82rqch34VnunrqOnzGS3iUN3bRjcQcBhgkc9a826gLBLIQr3KTmfA6s5ULknPkMUC2uccXkxPq1YmC6/5yX196gcLd8Hfw0kWX6X1oz3MzW8BNxEI3QxjaAMYIHnx1Oal2DahfQpLPHBJPpuyS2gYLuuGLckluu3JOBjNUBhu8j+my48feFaZUvoirC8mxuHXHnSpaZOPCS+/wDhx4WhkuZmsf6Vp+nRQXV3FJFe2pIkjwTncOeMnw5qCuq60qWw9nidrfcyPJGrNuJznnxGOPAfSsCLg3EixSYRcceuKkC0vHeNYzueQhVGOpP/AHoY4ko8oU6urMrPU9TeWMwaVbRXKyCRRb2+O9YeYB8+ccDxr2e11WS2uori4tYnuZEeRHuI1f3c7VGDgAZPFSb27t9KsdrbnhkBC7W2m8IIBZiP1M9E8uTycCDaw67qu2W102BYUDKu+IKrAkEjnk9P5yaXGLk7jFJD4cLg2z6prOnyRLeWcaxNbG1VWiDRTR/MHk8DoajW5mnMf9RFIGAUd2PdyeAoOcc+PWpsbTWL+yXUEVrLIpdrYN9xcoOoIz7rccMMEHxrH2NbZozYOhgnAeCWY7mxnGPmDxR4UoyrbQXLOg/ZG2bK++93neuTtx4V0AUhfZQAtpfDerHeu4qoUZx5Yp9Ferh9CJM/xGe0UUU0UFFFFYxhJ8B+VfPl2qx3Fy++ZMTsNu3J+I+o4r6Ek+A1wOYyNc3RW5IVJmyrIf7RqXU+CzSeSg1f726jb3pQfI7SB+dSdMhikzvV0CJuJMgwD+FaddR2v4UciUOBzHxjnx4qTpEwgikiNvtBX9fx48Tj+cUGNJ9nczlGNoyaM792w539d2R+zpUi0Yr3hIyN56f96hWUpnTiFlO4FsZxVrpuMyf4z1oYL36QGe9qslC9TeEnglePAwVHIqtdQVjKjgzEjPyphJjVwQ654qqS3a5urSFXRTNc7QzfCueMn0pmZOuBel9aBdPe+vLeGCNJJnyArAEHHz9BUh9GlF4LVrCNX25z3a4PGeuMdKY49AmspYL22vLcT229iJfhPBHI6jjPnzjw5q1WDVRKw9os0ULgGSAqdvw4xnPhmlwi/JfPKm+BEt9BvY5ZMWZUCQphVA5XOcfgeRUd9HngnmjmsWLoTvzFu/WI646cflTzOby4urVUvIHlkDyFxCQpGwbR1492TxxyPxyjTV445e91K1RXkbb90xPJL5GD098j6fgW0H2vHg59fab3VsZDaiMZIYtEBz5ZxUGdNtuuP7INdJ1y2vdRsGs5dTtAJHyB3TKDgM4BOf7jHJ8cDxpN7SaGdItYe8vI5maQIERSOFAJJ5IHUDFcaZ2M1VEJX2306kE5A6D0qyhnHf5jDq6W0rISCMEIfGokIA1C6z/YGP8Apqwu37mWG4RFl7n3jEP11xhl+qkitKDlikkebJr2llXaTw2Wv2dulrbSoYIgkkiZKkrksPDJPjinJ7ieY7WZVG34V4rnt4JbXVbDdiS2Ur7LcKP62LPGfUdCPAg/M9G0qzk1C6EEcJdW+Nt2BGPMn+etLxL3Ej0IOKjufQh9uh/4hbZ/9H95qX2fBk7OxnYpaO6dY3b9UYU5/wCo05dqLXsbo7IdWik1PUAuxLdWJOM+IyFUerfn0pXjnFxujtrGC0gjc93BCSEGepycZPrx8qLLjfH0AU9ztdD39lpUw3+zZjemdgHXFPQpH+y9Yltb3uggG9M7TnnFPAqvD6ETZ/iM9ooopokKKKD0rGMZPgNcGn7x7q6KXOMTsDvXjqcefrXeH+E1w6UGS6uWRo9qzNuDKBzk+nzqXU+CzS+Rb7QqWv4AqGQ7Scxjmum9ntI7O6f2Dh1fVbFwpi3XJAZmPvEDgda5x2jjWTUIAsLOpXkR59a6xpA09PsttRrFtNJZCH72FFLOR3nAAHJ5xXMPYWpvaU9l2f7JdpNJuL/sp3kcsD+8MOuW64Kt5jxqXq/Z7Srbs5Z3UNrsmm2F2DZJyuT+dRrDtJ2bsrNtN7M6beQ98+WZoGQZPiS3WrftBFPcdnrRImcsEhJCpnrnBpvu7rRK1Nx5FB9PgjJJjG04x5c01dl+y+m3OkRS6rArPPI3cqzY4xxj6KTVAdOuriS3hgeRGkcICyYDHPXp1wKe9W0m+ll0b9HPEkVhMJJAxI3YG3A+hb8a60mraAhafZzqTT1h7VW2nXCkp7asRUnG5Cwxz8qZ+3fZ1YrVNQsQ/wB0cTAsW93wP0/fWfbGwMXa3QL+MYWa6jjfjxDDB/CrfUdWW27SxaZdANaXsG33ugbOB+PT8KSoJJotlklJxlH5C8uk6LbdlrDV7u2O52j79xkkgnB4qw0XS+yOspcvYWjsIcCQsrL59M/I1n2xslsexHsaklIpEVT6buKrPsqJNnrOf7S//wB0dJT20C25Y3O/J4kPZPU9UsbGxtnZmmbvVdWGUCP4/wCILXmuRdg9GvTY6hYyiVQpwisw56eNUvYrI7ZWfH68o/8A1Y/upn7V9pU07WpLU9m/biqIfaO53dR0ztPSuRaaZ3JFxnSbfHzK/SNO7Oaz2tnis7VjBHagurgrls8ePlVnLpnZFdR9gksXSYv3YbnGfocj5mq/sdM9x2/1O49nNqklsMRFMeXI4q+a90g9oXgWx/pqS8SnkM2KZD0kclyLlx2H7rXZrCwljawki9oFvcruUNnB2+IOccitunala2V5NpGpyyaXvLCSdZMK5HgJDjaPp1qVcx6yO0zz6aUkkW2RkilIUCMkjbj555qTb9pLbVNTXTL7T0kuOY2Tg7Wz455FL2RjPclTHLdVeDl2r6daaZr12mnaiZ4XYMs6N3u/ODywPLf7VK0dGCyjLTFWPJOKtu3elW2kav3NpNLHE0e8IGLbck8VC0klgyZd8Hriky9fJYvhquh8+zKNI7a8WMKPeXO058PnTuKTPs4gEEF6AGG51PLZ8KcxVWH0Ihz/ABGe0UUU0UFBooNYxg/wmuHzR3PtM3dyW+O/O4KQPE+ldwf4TXEXjk9rnKW8TIZ23EO3mf71S6l9FmkXZTa1AJNWhAQgEclTnAzTnoOo3V/2WOizWzwW0ZSNLrBYD39x3eXhSP2ljUajCphk96P9Vv4Vd6JY3NtbNMfbLe2eJtymXasuP1SPr1pEZVyUZY2uRkFhZWeoXM8Eo23Snuo26pwCTz9SKmw65NbP3VriSNI1j3s2CoUAEAZ9KX4YFivrK7vjM0hRe7OzgjGPfHgdrdfCtTR9zc3kFuyPKsZlhUtjKg4zuPU+Yo4umr4Jbcl73BfzdrL9JIgNPDiBu8Qs2ckgqPyNLPaa+l1S4F7cqqSNIF2r0AxjFSdPv+/tzJK2ZQcOCMFOfKq3Wj9whXn7391MyTUsdxZzTpe1Vlvc9rbtrHT7eS1gk9lmjeN2JzlDkH931qFrmty65fR3F0I4mRdoCN9ar7Wyk1IOkbqohjMrlh4ZA/fTbp1pqFhawxCytZNkxUtJnOTIEORj4fdGPIc0iG+S5Z6M/Z43wuSt1HtffavpzabdJFtXZmUE7mI8fLmoPZztNPoEd9HBHA6zMN7OcHjcP300Wv6TW4Wcw2bgxqi7pGc+HPTgZTp4Et1rXay6t3InNlCivIQVkZiygMykDjhfdyo56k854JKV22J3Q27aVCjYavJpWpQ6jAkTyRMxCNnacgg/tNX5+1HVdoIsbXnGfeaq7WNJu717u+RlYiNp2j94kBSVx0HippRMy7oozgMSPp412LnHhBTWLJy0dC/+Mr9u073Is4WeK2EW0E9Cc5/KpMUl1fahFqRgRJZJgzRHoF/tZ8siluxIXtBcgkjciDgdav7lJGtZp7iZ4YkyEQjg+npkinRb2/U81xuXCsnz69IusW9nZtsvQRFMSniM5A8x8VGoduI7KeSK2KTXb4V3bJGBnBA8etKj6ZciHvYbhY7gys6S7yGSNiTtyP561Fisri2vY4RI8l40KokkfAcDJ35497Ph6VMs+7iL5HxhJeuHBjrWpXd1fyPf3cy3OffTldvlxmt+kfexS5aSU7sHaM4qHcNcld095N3m5g7sTlm/35qx0dg0Tgys5Xjhcn8aytsslW1UPv2cwLDFfbFcbnUkMPSnMUmfZxCIYr4K0jAup99ceFOYq3F6Dzs/xGe0UUUwSFFFFYxi/CmuKSMUuJlFuVDTMTgnnn1rtb/Ca5VcwWKyTHvhjvGyDJ60nMrKdPKrFfUNPkv9chhi7xU7vDP1CY5548ql67qzW2kNYzHeY1xFKf8AzePhUAcHGcj0FWF+YrWJpbWTLBfeAkAZhjwzx4ilXULoXkEXfYIYgq2ACCPXypMMUpT5Nmyz8E2xu/07ENTe5u1ljk2zRtINwzxnAwOnp881UNqLXWuK1okpkKlAWf3TGDx1/cB1rda3Rgs2SDiVw3eAgYxxzVZHGYZFkjZ1dBgMD8NMWmk2yRtyfJYu/sF1KsV3I06nLwvnnPj6jpVrNKbiAd6j5zkYGOQp/KqJHLzG4kO+ViAXPU1d3bH2CLz39fpSsuJwXJTpovemawyofffG4DxxnmtvtTE7jcE48d5z+2ojciLzDccCnB7nT10aymUWCygxBk2xyB8nD8EBlOOuQR5GkQW5dnq5JuL6Ky30+9Jl2uEWMsGbfgceXp/vUK9F5ZyKlxKULDIxKPDp0+Ypmd9MuRqyTXdlHNNNLHaBOO7jjJ2HjjLE+PUAdakxy9mnv7t45LIP3qp3UzKVBEbYdSeNrErn1U+dNWNfMR7eX5RFllbYczkhhg++ecn/AL1S3M8Ju1QqC2QoPlTN2oa13wrb+yGb2ZPavZQO773JzjHHTHSlhhGLqKPu1Dthu8I5GP8AvXIquA5tuKaQ4vH7DfSX8h9wMoIB8RgjFWMuqW1xbOsEqFpZSY+9A38c9D4+Rpc1y5YXU4ONixr7o8eKoe/7ueC4iLb42zHgZwaKOB5ItpnmyySg+B9N/LDeyHuu6tnBO44HvH+75nBP1NL+r9r43RzbSBJ4GxCHX4+QTjxx4enXpVfrGqNqEsYcYjjJzERhQ56nH84qBFHBkHulBxjJHSth0WypPwcWoyJPksLXULnUGdhI8Q+M7TkBj5mmPSJHSJxJPh14JAzn8SKV0fu4oxbbY+8YAgjg9evnThottGF3yzBnK84NHPG0x2PLJ9jr9nSbYb37xny6n3lxjj504iljsQkaRXQjIPK55z4U0VRBVERldzbCiiijFhRRRWMYv8J+VcOnLJqMge6iCtKT7y8AZ58K7jJjYc1yRrGwv7uXuZiVVyRHIvLjJzg/z0qfPXFj8GSMG9xXalcW1xpsns5hDCNgWA6nHHhSesm60hz75Em3HpzT9c6NOtvJ3ctqLcoxMo42jHXBrm6FljlRyTtlwRjGOcV3TzTyUgs0lKFombsKoHORjJ47z51rBBI5LZ4HHxelYlhz5+OP3VgW5PTpz/D1q8jN0Xvd2v8AabNX18NunxZ/9QfsqhsQHliph1YbdOhz/wCqP2V5+qLNN60V5PwA/wBqrCzsri5IeKzuJYVYd48MbMFHqQDjiqlnyFJPIPWmns7rVpY2eyVroOl0lwvs5xv2j4W9DUUIq+T1crkl7p7DosMjLLFHeiKZV2Otu4QnHGGK4OcnkE5xxUC90nuJZFtYrx5u8VWja2cEEgnHIGSeo888dKvT2psJms5Qs8M1tHGrLHECBgHODnkc9MV4O1+mq1zFBBP3cojjlbODgK4Ypz7h94cDjr507bAm3ZV4Ey7BWJ1K7XHByPWoFxCDPFNuwFAGKmThFSTuiSn6pIwSKjyHCRD1WlrgfNWi315Abq64/wDLXH4UthiVjPjuGKa9Xj33t0B4qtKG7CgeRq/S9M8fNwyRnPXkn061khwOPeyM8/rfP5VpLDHzoZsK5Y9fL9oqpiTcxIeyQHzJz9MU6WCy9yCjR56Dn+FIrSD9KW6luFUAj50+aV3zKgiCNjBJJGB86iy9lmFqK5Hv7PWZo77cQcOo4+VOApV7BxKkF2QY9xcbgngcU1Ciw+hCczTm2j2iiimiwooorGMZPgb5VyW9aO2nkt1LvL3rZWRQAoJ4HrXWpPgPyriN9FEWuSJzJeiZ5NiuFxk465zgBfpU2oVqgZKye3dWS93K7r7SdwUJ8ZJ/Lnml3tBor3PtN/YtCIIbZGkVQFPu8ZAHU9Pwq1a9E9okBlinix9yqyMys3Q48dxGcc4GOlL3aW6ht2ksraQQtIoWaEEtkDn4vn1FKwJxmqNG1aKEykk4U4+teb/pXkbkopbqQM/hQ7cH5V6xwtNGG+4UVfdoPc0uL/OH7KpOzid5MT5VfdoR/wCGRf5w/ZUGftlmm9SKAsQUwcHqDTaIknuLcPaxNcXNp7UGbJARQxIx58UqRTFfgd09VaryLtPexpb7YrF2t4+6SR4nLumCCG9/BznngVJBpdnpZNz9Jeafo73EcN5a21vGbiGGWMSbiT3gYqCQOMY6/jXsOiOVsxJbW7zXW134fD5Un48Yz1OAc8YqkTtjqsZjEJt4o4tgSNEfaFQFVU5bJXk5ycnzrW3au9YQCS005+5UKhMUh90DABBfH1xnypqcLEbMpo7TRwWbraJBtmHLtFGyggjjryD6HpVI7Z7v0K/tq11PVrvVrhZrllQpGsaohbAUdOWJJPqTVLO33if4x4+tA0m+A+Yw5HG4UPqdwpGcxrSNeARzOAMBZCCPrT+edZl/y1NImuJ3WpXaeUhP4gGq9N2zzcpHErHnA65PFZgjaAzEZ6fKsAen85oJ5Uf3v41WT2WnZu3g1HUtUimRTKlqZYM9QyEEgfMZp+0W7s9JgjnupdveLztyRj91IItY9L1bTrqzdgLq2Ej7iCQxyrDp0PlTbptpDcywJdRhoYsvIrdHAHIPoemPWoM6clwVbW48HQ+wdwk6XwgcPAHUx46gFeh9abKS/s2wbe/Ixgzk8ADrk06U3EqjQlqgooophwKKKKxjF/gPyr5+v7MwapJsDRkvJyD1yen1r6Bc4U/KuF77jUbqdu6DQxzOrkjpyfDrSM3gJLhkGRZoooreKVGhVy6xFuTnOPLz86WdYeM6hF3bsW6PnHXB/nxpnm3CaRzGW9zBOcjnjnArXednxJDJcHO5ASNkJxkc0vEveNB2hVjGV+RI/OvHHukVJNu4LdOTnpWDQtjqPwr0rQsuezDwgFpJY0O7HvNirvWEgv7RIY7+1UiTcSzdBilbT9NmNrPP7OxJB2Ns6n0/KmK306yVW7+3y+cK3dnIGB+/mvMz5NsizFB0RU0qFeDq9q3yH8az/Rlr/wDlLf8An61vax0pvits56/dmtTadpg4S1GP8o1P7aPyKHv+ZiNMtc//ADW3/n61l+i7M9dWg+gH+9ZJp9h0W3Hy7s1sGn2Qbm1T6oa3tl+U22f5jSNIsj/94hHqV/jWltAsmcMNetjgg42AdOeu70q0t7WzikWSO0Qup43RkgfSrEm1mwZ9JtXP9232k/gaJZ/oDKMn5I26F9Qadby22lQAO85/ZS7qWjyatrF37JNbBVCszzTBVwRjx69KZrbS9IluZPaYJoY291F7lZFXHQk/XHSqXtJoPsM63NrEhhPunauAc+JHlVOCRLljRDtOy08klzbd4vt8CLMluuGE0fjtYfsrdpC2McV/pt7bM0jRkuSisyY6OhxkEHGRnmvNNjmj7m4tJTEYidhVTlcAYz6jp6jHriZcpLJq/tQysrIVZgMg5Jz+78adLL4EJFJsnle1STDi39yMAc7WO4fnn8BTlah7SIMsbMZSAc4OBkHy9KiafpzLFEHG2RSpyOo2kEfv/KrKRzAQy/eFnJAU8dOmMZ61O5lCaSHTsEoUX+0uVMgPveHHQU20lfZqcw6hksT3oJLHOSRnj0p1p+L0iX2FFFFMOBRRRWMYv8Jrid6jQT3E9uQFaXY7ZACsSxH5V2x/hNcPvbia9kmsBt2d+8igryX5/PjgetIzeAZFZeTd/dpH3uI9oC930b/b51Kk1Ca3thGjyhUB245qqXuzdu+H3McY6Yx14/npUx3musG2ijKxpnLcED19PSlLgOPurgtYXilRBEzHCKGEkYGGwM/TNbhGisMiPOeRgVp07VIYlhhaLcFGCwTjP4dK33l/DmHaiwkvhm2AgjPI/jTFK/I6M00ifDMkahd8eB04HFb/AGgY+JfwFEV1ad0p9mVz5gAZr32u3/5P8q3HY1Pgx9oI5yPwFeG9Ycb+T4Vk15B/y1Ym8gOP6MK3BrAXkni9AvX6mT8K8N5Dji1zWK3kI62o61jWbheOf1zx616Lx2cAybSeNxOAK1+1W7f/AEw49aPbY8cWoHyNcM2b57kbQI7oufE4wAfQ+NRZLguQmQT1A46+Ne+1Kzb1tkOOo86izz95nER3fFjH7KVJ10LbNUcilCgUcEj588fWiZu8t1uFCjHBPOfIeHn1qPJcu74EeHbqFxz+H0qHcStHcoDtHe4Lr18CP2H8qU227FWTDdgxySA42xHK55HH8PzrC0la5ZkLxrIc/duwXPoCfGqsuVZgASCwC55BA5AP4V5bhvakSQAHIGc55PjXAbOofZsJEj1KKWIxtHMoYcddueMU6Uj/AGXHNrfHbgiVQx8zjmnmrsPMEZhRRRTjh//Z',
  },
  {
    publication: 'Eco Living Weekly',
    quote:
      "More than just beautiful wooden animal figurines, Cokugoo's commitment to sustainability makes their art a choice you can feel good about.",
    logo: 'data:image/jpeg;base64,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',
  },
]

export function MediaCoverage() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-medium text-gray-800 mb-6">
            As Featured: Cokugoo's Wood Carving Art
          </h2>
          <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
            We are honored to have our wooden figurines recognized by leading
            publications and partners who appreciate the art of wood carvings of
            animals.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {mediaReports.map((report, index) => (
            <motion.div
              key={report.publication}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
            >
              <div className="flex items-center justify-center mb-6">
                {/* 书本样式容器 */}
                <div className="relative group">
                  {/* 书本主体 */}
                  <div
                    className="relative w-20 h-24 bg-transparent rounded-lg shadow-lg transition-transform duration-300"
                    style={{
                      transform: 'perspective(1000px) rotateY(-15deg)',
                      transformStyle: 'preserve-3d',
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform =
                        'perspective(1000px) rotateY(-8deg)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform =
                        'perspective(1000px) rotateY(-15deg)'
                    }}
                  >
                    {/* 书脊 */}
                    <div className="absolute left-0 top-0 w-2 h-full bg-gradient-to-b from-gray-100 to-gray-200 rounded-l-sm shadow-inner"></div>

                    {/* 书页效果 */}
                    <div className="absolute right-0 top-1 w-0.5 h-22 bg-gray opacity-60"></div>
                    <div className="absolute right-1 top-1 w-0.5 h-22 bg-gray opacity-40"></div>
                    <div className="absolute right-2 top-1 w-0.5 h-22 bg-gray opacity-20"></div>

                    {/* Logo容器 */}
                    <div
                      className={`absolute inset-0 pl-2 rounded-xl overflow-hidden flex items-center justify-center shadow-inner ${
                        report.logoClassName || ''
                      }`}
                    >
                      <img
                        className="w-full h-full rounded-sm py-[0.1px] object-cover p-0"
                        src={report.logo}
                        alt={report.publication}
                      />
                    </div>

                    {/* 书本光泽效果 */}
                    <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-r-lg pointer-events-none"></div>
                  </div>

                  {/* 书本阴影 */}
                  <div
                    className="absolute -bottom-1 -right-1 w-20 h-24 bg-gray-300/30 rounded-r-lg blur-sm -z-10"
                    style={{
                      transform:
                        'perspective(1000px) rotateY(-15deg) translateZ(-10px)',
                    }}
                  ></div>
                </div>
              </div>

              <blockquote className="text-gray-700 text-center mb-6 leading-relaxed italic">
                "{report.quote}"
              </blockquote>

              <div className="text-center">
                <div className="font-semibold text-gray-900 text-lg">
                  {report.publication}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
