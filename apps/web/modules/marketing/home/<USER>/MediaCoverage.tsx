'use client'

import { motion } from 'framer-motion'
import { Quote } from 'lucide-react'

const mediaReports = [
  {
    publication: 'Artisanal Home Magazine',
    quote:
      "Cokugoo is reviving the charm of classic wood carved animals with a modern, soulful twist that's perfect for contemporary homes.",
    logo: '/img-1.jpg',
  },
  {
    publication: 'The Woodcraft Journal',
    quote:
      'Their collection of hand carved wooden animals showcases exceptional skill and a deep respect for the material, a true gem for collectors.',
    logo: '/img-1.jpg',
  },
  {
    publication: 'Eco Living Weekly',
    quote:
      "More than just beautiful wooden animal figurines, Cokugoo's commitment to sustainability makes their art a choice you can feel good about.",
    logo: '/img-1.jpg',
  },
]

export function MediaCoverage() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            As Featured: Cokugoo's Wood Carving Art
          </h2>
          <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
            We are honored to have our wooden figurines recognized by leading
            publications and partners who appreciate the art of wood carvings of
            animals.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {mediaReports.map((report, index) => (
            <motion.div
              key={report.publication}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
            >
              <div className="flex items-center justify-center mb-6">
                <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center">
                  <Quote className="w-8 h-8 text-amber-600" />
                </div>
              </div>

              <blockquote className="text-gray-700 text-center mb-6 leading-relaxed italic">
                "{report.quote}"
              </blockquote>

              <div className="text-center">
                <div className="font-semibold text-gray-900 text-lg">
                  {report.publication}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
