'use client'

import Image from 'next/image'
import { motion } from 'framer-motion'
import { useState, useRef, useEffect } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'

const designers = [
  {
    name: '<PERSON>',
    description:
      '<PERSON> finds joy in the details, specializing in whimsical wooden figurines that bring a smile. Her work is a celebration of childhood wonder and intricate craftsmanship.',
    image: '/img-1.jpg',
  },
  {
    name: '<PERSON>',
    description:
      "A master of form, <PERSON>'s signature is the powerful yet graceful carved wood bear. He draws inspiration from the raw strength and quiet dignity of mountain wildlife.",
    image: '/img-1.jpg',
  },
  {
    name: '<PERSON>',
    description:
      "<PERSON>'s passion is capturing movement. Her dynamic fox wooden sculptures are known for their fluid lines and lively expressions, making the wood feel alive.",
    image: '/img-1.jpg',
  },
  {
    name: '<PERSON>',
    description:
      '<PERSON> is a storyteller who uses wood as his medium. His collections of wood carved animals often depict folklore and fables, turning each piece into a conversation starter.',
    image: '/img-1.jpg',
  },
]

export function DesignerProfiles() {
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(true)

  // Check scroll position and update navigation states
  const checkScrollPosition = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } =
        scrollContainerRef.current
      setCanScrollLeft(scrollLeft > 0)
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 10)
    }
  }

  useEffect(() => {
    checkScrollPosition()
    const container = scrollContainerRef.current
    if (container) {
      container.addEventListener('scroll', checkScrollPosition)
      return () => container.removeEventListener('scroll', checkScrollPosition)
    }
  }, [])

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current
      const cardWidth = container.clientWidth / 3
      const gap = 16 // 4 * 4px (gap-4)
      const scrollAmount = (cardWidth + gap) * 0.9 // 调整滚动距离

      container.scrollBy({
        left: -scrollAmount,
        behavior: 'smooth',
      })
    }
  }

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current
      const cardWidth = container.clientWidth / 3
      const gap = 16 // 4 * 4px (gap-4)
      const scrollAmount = (cardWidth + gap) * 0.9 // 调整滚动距离

      container.scrollBy({
        left: scrollAmount,
        behavior: 'smooth',
      })
    }
  }

  return (
    <section className="py-16 md:py-24" style={{ backgroundColor: '#f7f7f8' }}>
      <div className="container mx-auto px-4">
        {/* Header with navigation - similar to example.html */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center mb-8">
            <button
              onClick={scrollLeft}
              disabled={!canScrollLeft}
              className={`p-2 transition-colors duration-300 ${
                canScrollLeft
                  ? 'text-gray-600 hover:text-gray-900'
                  : 'text-gray-300 cursor-not-allowed'
              }`}
              aria-label="Previous"
            >
              <ChevronLeft size={24} strokeWidth={1.5} />
            </button>

            <div className="mx-8">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-medium text-gray-800 mb-6">
                Meet the Artists Behind Our Wood Carved Animals
              </h2>
              <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
                The soul of our wood carving art lies in our creators. Meet the
                talented designers whose hands and hearts shape our unique wood
                animal carvings.
              </p>
            </div>

            <button
              onClick={scrollRight}
              disabled={!canScrollRight}
              className={`p-2 transition-colors duration-300 ${
                canScrollRight
                  ? 'text-gray-600 hover:text-gray-900'
                  : 'text-gray-300 cursor-not-allowed'
              }`}
              aria-label="Next"
            >
              <ChevronRight size={24} strokeWidth={1.5} />
            </button>
          </div>
        </motion.div>

        {/* Carousel container - inspired by example.html product carousel */}
        <div className="relative">
          {/* 为阴影预留更多空间的包装器 */}
          <div className="px-8 py-12 -mx-8 -my-12 overflow-visible">
            <div
              ref={scrollContainerRef}
              className="flex overflow-x-auto gap-4 overflow-y-hidden items-start"
              style={{
                scrollbarWidth: 'none',
                msOverflowStyle: 'none',
                WebkitOverflowScrolling: 'touch',
              }}
              onWheel={(e) => {
                // 只有当水平滚动到边界时才允许页面滚动
                const { scrollLeft, scrollWidth, clientWidth } = e.currentTarget
                const isAtStart = scrollLeft === 0
                const isAtEnd = scrollLeft >= scrollWidth - clientWidth - 1

                if (e.deltaX !== 0) {
                  // 水平滚动，阻止页面滚动
                  e.preventDefault()
                  const scrollAmount = e.deltaX * 1.2 // 降低滚动灵敏度
                  e.currentTarget.scrollBy({
                    left: scrollAmount,
                    behavior: 'auto',
                  })
                } else if (e.deltaY !== 0 && !isAtStart && !isAtEnd) {
                  // 垂直滚动且不在边界，转换为水平滚动
                  e.preventDefault()
                  const scrollAmount = e.deltaY * 0.8 // 降低滚动速度
                  e.currentTarget.scrollBy({
                    left: scrollAmount,
                    behavior: 'auto',
                  })
                }
                // 在边界时允许页面正常滚动
              }}
            >
              {/* 隐藏滚动条的样式 */}
              <style jsx>{`
                div::-webkit-scrollbar {
                  display: none;
                }
              `}</style>
              {designers.map((designer, index) => (
                <motion.div
                  key={designer.name}
                  // initial={{ opacity: 0, y: 30 }}
                  // whileInView={{ opacity: 1, y: 30 }}
                  // transition={{ duration: 0.6, delay: index * 0.1 }}
                  // viewport={{ once: true }}
                  // className="flex-none w-full md:w-[calc(32.6%-16px)] bg-white rounded-2xl overflow-hidden transition-all duration-300 transform hover:-translate-y-3 mx-2"
                  style={{ scrollSnapAlign: 'start' }}
                >
                  {/* Designer image */}
                  <div className="relative h-72 overflow-hidden">
                    <Image
                      src={designer.image}
                      alt={`${designer.name} - Wood Carving Artist`}
                      fill
                      className="object-cover h-[60%] hover:scale-110 transition-transform duration-500"
                    />
                    {/* Overlay similar to example.html */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent" />

                    {/* Name overlay on image */}
                    <div className="absolute bottom-4 left-4 right-4">
                      <h3 className="text-2xl font-bold text-white mb-1 drop-shadow-lg">
                        {designer.name}
                      </h3>
                    </div>
                  </div>

                  {/* Designer content */}
                  <div className="py-6">
                    <p className="text-gray-700 items-start leading-relaxed">
                      {designer.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
