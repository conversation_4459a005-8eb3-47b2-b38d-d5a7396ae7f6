'use client'

import Image from 'next/image'
import { motion } from 'framer-motion'

const designers = [
  {
    name: '<PERSON>',
    description:
      '<PERSON> finds joy in the details, specializing in whimsical wooden figurines that bring a smile. Her work is a celebration of childhood wonder and intricate craftsmanship.',
    image: '/img-1.jpg',
  },
  {
    name: '<PERSON>',
    description:
      "A master of form, <PERSON>'s signature is the powerful yet graceful carved wood bear. He draws inspiration from the raw strength and quiet dignity of mountain wildlife.",
    image: '/img-1.jpg',
  },
  {
    name: '<PERSON>',
    description:
      "<PERSON>'s passion is capturing movement. Her dynamic fox wooden sculptures are known for their fluid lines and lively expressions, making the wood feel alive.",
    image: '/img-1.jpg',
  },
  {
    name: '<PERSON>',
    description:
      '<PERSON> is a storyteller who uses wood as his medium. His collections of wood carved animals often depict folklore and fables, turning each piece into a conversation starter.',
    image: '/img-1.jpg',
  },
]

export function DesignerProfiles() {
  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-amber-50 to-orange-50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Meet the Artists Behind Our Wood Carved Animals
          </h2>
          <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
            The soul of our wood carving art lies in our creators. Meet the
            talented designers whose hands and hearts shape our unique wood
            animal carvings.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 lg:gap-12">
          {designers.map((designer, index) => (
            <motion.div
              key={designer.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
            >
              <div className="relative h-64 overflow-hidden">
                <Image
                  src={designer.image}
                  alt={`${designer.name} - Wood Carving Artist`}
                  fill
                  className="object-cover hover:scale-110 transition-transform duration-500"
                />
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {designer.name}
                </h3>
                <p className="text-gray-700 leading-relaxed">
                  {designer.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
