'use client'

import { Button } from '@ui/components/button'
import { motion } from 'framer-motion'

export function ValueProposition() {
  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-amber-50 to-orange-50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center max-w-4xl mx-auto"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Artisanal Wooden Figurines, Crafted with Passion
          </h2>
          
          <p className="text-lg md:text-xl text-gray-700 mb-8 leading-relaxed">
            We celebrate the timeless appeal of wood carved animals. Our commitment to traditional folk art wood carving ensures every piece is a unique treasure.
          </p>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <Button 
              size="lg" 
              className="bg-amber-600 hover:bg-amber-700 text-white px-8 py-4 text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              Explore Our Hand Carved Wooden Animals
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
