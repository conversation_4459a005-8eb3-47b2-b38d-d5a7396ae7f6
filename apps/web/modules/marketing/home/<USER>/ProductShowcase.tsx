'use client'

import Image from 'next/image'
import { Button } from '@ui/components/button'
import { motion } from 'framer-motion'

const products = [
  {
    id: 1,
    name: 'Wooden Fox Figurine',
    image: '/img-1.jpg',
    price: '$45.00',
  },
  {
    id: 2,
    name: 'Carved Bear Sculpture',
    image: '/img-1.jpg',
    price: '$65.00',
  },
  {
    id: 3,
    name: 'Wooden Cat Figurine',
    image: '/img-1.jpg',
    price: '$38.00',
  },
  {
    id: 4,
    name: 'Eagle Wood Carving',
    image: '/img-1.jpg',
    price: '$85.00',
  },
  {
    id: 5,
    name: 'Rabbit Wooden Sculpture',
    image: '/img-1.jpg',
    price: '$42.00',
  },
  {
    id: 6,
    name: 'Owl Wood Figurine',
    image: '/img-1.jpg',
    price: '$55.00',
  },
]

export function ProductShowcase() {
  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Our Gallery of Wood Carved Animals
          </h2>
          <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
            From playful wooden cat figurines to majestic eagles, explore our
            diverse collection of wooden figurines. Find the perfect piece that
            speaks to you.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 mb-12">
          {products.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
            >
              <div className="relative h-64 overflow-hidden">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-cover hover:scale-110 transition-transform duration-500"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {product.name}
                </h3>
                <p className="text-2xl font-bold text-amber-600">
                  {product.price}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Button
            size="lg"
            className="bg-amber-600 hover:bg-amber-700 text-white px-8 py-4 text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            View All Wooden Figurines
          </Button>
        </motion.div>
      </div>
    </section>
  )
}
