'use client'

import Image from 'next/image'
import { Button } from '@ui/components/button'
import { motion } from 'framer-motion'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { useState, useEffect } from 'react'

const products = [
  {
    id: 1,
    name: 'Wooden Fox Figurine',
    image: '/img-1.jpg',
    price: '$45.00',
  },
  {
    id: 2,
    name: 'Carved Bear Sculpture',
    image: '/img-1.jpg',
    price: '$65.00',
  },
  {
    id: 3,
    name: 'Wooden Cat Figurine',
    image: '/img-1.jpg',
    price: '$38.00',
  },
  {
    id: 4,
    name: 'Eagle Wood Carving',
    image: '/img-1.jpg',
    price: '$85.00',
  },
  {
    id: 5,
    name: 'Rabbit Wooden Sculpture',
    image: '/img-1.jpg',
    price: '$42.00',
  },
  {
    id: 6,
    name: 'Owl Wood Figurine',
    image: '/img-1.jpg',
    price: '$55.00',
  },
]

export function ProductShowcase() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const itemsPerView = 4 // Show 4 products at a time
  const maxIndex = Math.max(0, products.length - itemsPerView)

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev >= maxIndex ? 0 : prev + 1))
    }, 4000)

    return () => clearInterval(interval)
  }, [isAutoPlaying, maxIndex])

  const goToPrevious = () => {
    setIsAutoPlaying(false)
    setCurrentIndex((prev) => (prev <= 0 ? maxIndex : prev - 1))
    setTimeout(() => setIsAutoPlaying(true), 5000)
  }

  const goToNext = () => {
    setIsAutoPlaying(false)
    setCurrentIndex((prev) => (prev >= maxIndex ? 0 : prev + 1))
    setTimeout(() => setIsAutoPlaying(true), 5000)
  }

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        {/* Title with Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center mb-6">
            <button
              onClick={goToPrevious}
              className="p-2 hover:bg-gray-100 transition-colors mr-6"
              aria-label="Previous products"
            >
              <ChevronLeft className="w-6 h-6 text-gray-600" />
            </button>
            <a href="#gallery" className="text-center">
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-medium text-gray-900 hover:text-gray-700 transition-colors">
                Our Gallery of Wood Carved Animals
              </h2>
            </a>
            <button
              onClick={goToNext}
              className="p-2 hover:bg-gray-100 transition-colors ml-6"
              aria-label="Next products"
            >
              <ChevronRight className="w-6 h-6 text-gray-600" />
            </button>
          </div>
        </motion.div>

        {/* Products Grid */}
        <div className="relative overflow-hidden mb-12">
          <motion.div
            className="flex transition-transform duration-500 ease-in-out"
            style={{
              transform: `translateX(-${currentIndex * (100 / itemsPerView)}%)`,
            }}
          >
            {products.map((product, index) => (
              <motion.div
                key={product.id}
                className="flex-shrink-0 px-3"
                style={{ width: `${100 / itemsPerView}%` }}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="group relative bg-gray-50 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300">
                  {/* Product Labels */}
                  <div className="absolute flex items-center top-3 left-3 z-10 space-x-2">
                    <span className="bg-white text-gray-600 text-xs px-2 py-1 rounded">
                      New in
                    </span>
                    <span className="bg-red-500 text-white text-xs px-2 py-1 rounded block">
                      18% off
                    </span>
                  </div>

                  {/* Product Image */}
                  <div className="relative h-64 bg-gray-100">
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-500"
                    />

                    {/* Image Navigation Buttons */}
                    <button className="absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-white/80 hover:bg-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                      <ChevronLeft className="w-4 h-4 text-gray-600" />
                    </button>
                    <button className="absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-white/80 hover:bg-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                      <ChevronRight className="w-4 h-4 text-gray-600" />
                    </button>

                    {/* Quick Buy Button */}
                    <div className="absolute bottom-3 left-1/2 -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <a
                        href="#quick-buy"
                        className="bg-white text-gray-700 text-sm px-4 py-2 rounded hover:bg-gray-50 transition-colors"
                      >
                        Quick buy
                      </a>
                    </div>
                  </div>

                  {/* Product Info */}
                  <div className="p-4 text-center">
                    <a href="#product" className="block">
                      <h3 className="text-sm font-medium text-gray-900 mb-2 hover:text-gray-700 transition-colors">
                        {product.name}
                      </h3>
                      <div className="space-y-1">
                        <span className="text-lg font-semibold text-red-600">
                          {product.price}
                        </span>
                        <div className="text-sm text-gray-500">
                          <span className="line-through mr-2">$55.00</span>
                          <span className="text-red-600 font-medium">Sale</span>
                        </div>
                      </div>
                    </a>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Description */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed mb-8">
            From playful wooden cat figurines to majestic eagles, explore our
            diverse collection of wooden figurines. Find the perfect piece that
            speaks to you.
          </p>
          <a
            href="#all-products"
            className="text-gray-700 hover:opacity-80 text-lg font-semibold underline hover:no-underline transition-all duration-300"
          >
            View All Wooden Figurines
          </a>
        </motion.div>
      </div>
    </section>
  )
}
