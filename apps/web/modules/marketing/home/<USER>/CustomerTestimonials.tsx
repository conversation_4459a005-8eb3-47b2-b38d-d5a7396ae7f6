'use client'

import { motion } from 'framer-motion'
import { Star, MapPin } from 'lucide-react'

const testimonials = [
  {
    name: '<PERSON>',
    location: 'Austin, TX',
    review:
      'The level of detail in the wood carving art is breathtaking. My fox figurine is now the centerpiece of my collection. Absolutely stunning quality!',
    rating: 5,
  },
  {
    name: '<PERSON>',
    location: 'London, UK',
    review:
      'I ordered one of the wooden cat figurines for my wife, and she was overjoyed. The craftsmanship is incredible, and it arrived beautifully packaged.',
    rating: 5,
  },
  {
    name: '<PERSON>',
    location: 'Sydney, AU',
    review:
      "I've been searching for authentic carved wooden animals for ages. <PERSON><PERSON><PERSON><PERSON>'s pieces have a warmth and character that is simply unmatched. Highly recommended.",
    rating: 5,
  },
  {
    name: '<PERSON>',
    location: 'Toronto, CA',
    review:
      "These are not just decorations; they are heirloom-quality wooden figurines. I purchased a bear for my son, and it's a piece we'll treasure forever.",
    rating: 5,
  },
]

export function CustomerTestimonials() {
  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-amber-50 to-orange-50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Hear From Our Collectors of Wooden Figurines
          </h2>
          <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Nothing makes us happier than seeing our wood carved animals find a
            new home. See what our customers say about our hand carved santas
            and more.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
            >
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star
                    key={i}
                    className="w-5 h-5 text-yellow-400 fill-current"
                  />
                ))}
              </div>

              <blockquote className="text-gray-700 mb-6 leading-relaxed italic">
                "{testimonial.review}"
              </blockquote>

              <div className="flex items-center justify-between">
                <div>
                  <div className="font-semibold text-gray-900">
                    {testimonial.name}
                  </div>
                </div>
                <div className="flex items-center text-gray-500 text-sm">
                  <MapPin className="w-4 h-4 mr-1" />
                  {testimonial.location}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
