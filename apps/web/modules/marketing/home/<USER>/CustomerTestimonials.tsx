'use client'

import { motion } from 'framer-motion'
import { Star, MapPin } from 'lucide-react'
import { useState, useEffect } from 'react'
import Image from 'next/image'

const testimonials = [
  {
    name: '<PERSON>',
    location: 'Austin, TX',
    review:
      'The level of detail in the wood carving art is breathtaking. My fox figurine is now the centerpiece of my collection. Absolutely stunning quality!',
    rating: 5,
    productImage: '/img-1.jpg',
    productName: 'Fox Figurine',
    productLink: '#',
  },
  {
    name: '<PERSON>',
    location: 'London, UK',
    review:
      'I ordered one of the wooden cat figurines for my wife, and she was overjoyed. The craftsmanship is incredible, and it arrived beautifully packaged.',
    rating: 5,
    productImage: '/img-1.jpg',
    productName: 'Wooden Cat Figurine',
    productLink: '#',
  },
  {
    name: '<PERSON>',
    location: 'Sydney, AU',
    review:
      "I've been searching for authentic carved wooden animals for ages. Cokugo<PERSON>'s pieces have a warmth and character that is simply unmatched. Highly recommended.",
    rating: 5,
    productImage: '/img-1.jpg',
    productName: 'Carved Wooden Animals',
    productLink: '#',
  },
  {
    name: '<PERSON>',
    location: 'Toronto, CA',
    review:
      "These are not just decorations; they are heirloom-quality wooden figurines. I purchased a bear for my son, and it's a piece we'll treasure forever.",
    rating: 5,
    productImage: '/img-1.jpg',
    productName: 'Bear Figurine',
    productLink: '#',
  },
]

export function CustomerTestimonials() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prev) =>
        prev >= testimonials.length - 1 ? 0 : prev + 1
      )
    }, 5000)

    return () => clearInterval(interval)
  }, [isAutoPlaying])

  const currentTestimonial = testimonials[currentIndex]

  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-amber-50 to-orange-50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="text-sm font-medium text-gray-600 mb-4 uppercase tracking-wider">
            From The People
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Hear From Our Collectors of Wooden Figurines
          </h2>
          <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Nothing makes us happier than seeing our wood carved animals find a
            new home. See what our customers say about our hand carved santas
            and more.
          </p>
        </motion.div>

        {/* Testimonial Slider */}
        <div className="max-w-6xl mx-auto">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.5 }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center"
          >
            {/* Text Content */}
            <div className="order-2 lg:order-1">
              <div className="text-sm font-medium text-gray-600 mb-4 uppercase tracking-wider">
                From The People
              </div>

              {/* Star Rating */}
              <div className="flex items-center mb-6">
                {[...Array(currentTestimonial.rating)].map((_, i) => (
                  <Star
                    key={i}
                    className="w-6 h-6 text-amber-400 fill-current"
                  />
                ))}
              </div>

              {/* Review Text */}
              <blockquote className="text-xl md:text-2xl text-gray-800 leading-relaxed mb-6 font-medium">
                "{currentTestimonial.review}"
              </blockquote>

              {/* Author */}
              <div className="text-gray-600 text-lg">
                — {currentTestimonial.name}
              </div>
            </div>

            {/* Product Image */}
            <div className="order-1 lg:order-2">
              <motion.div
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
                className="relative"
              >
                <a
                  href={currentTestimonial.productLink}
                  className="block group"
                >
                  <div className="relative h-80 w-full rounded-2xl overflow-hidden shadow-lg">
                    <Image
                      src={currentTestimonial.productImage}
                      alt={currentTestimonial.productName}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                  </div>
                  <div className="mt-4 text-center">
                    <span className="text-gray-800 font-medium underline group-hover:text-amber-600 transition-colors">
                      Shop {currentTestimonial.productName}
                    </span>
                  </div>
                </a>
              </motion.div>
            </div>
          </motion.div>

          {/* Navigation Dots */}
          <div className="flex justify-center mt-12 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  setCurrentIndex(index)
                  setIsAutoPlaying(false)
                  setTimeout(() => setIsAutoPlaying(true), 5000)
                }}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? 'bg-amber-600 scale-125'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
