'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown } from 'lucide-react'
import { Button } from '@ui/components/button'

const faqs = [
  {
    question: 'How do you ensure the quality of your hand carved wooden animals?',
    answer: 'Each piece undergoes a rigorous quality check. Our artists are masters of wood carving art, using high-grade, sustainably sourced wood. From the initial sketch to the final polish, every step is handled with meticulous care to ensure perfection.',
  },
  {
    question: 'Can I request a custom design for a wooden bear figurine?',
    answer: 'While we don\'t currently offer fully custom designs, we are always expanding our collection of wood carved animals. We recommend joining our newsletter to be the first to know about new creatures and limited-edition releases from our artists.',
  },
  {
    question: 'What makes your wood carving art different from others?',
    answer: 'Our focus is on authentic folk art wood carving. Unlike mass-produced items, each of our pieces is individually designed and carved by a dedicated artist, imbuing it with a unique character and soul that cannot be replicated by machine.',
  },
  {
    question: 'Do you have seasonal items like a hand carved wooden santa claus?',
    answer: 'Yes! We release seasonal and limited-edition collections, including our popular Santas. These special wooden figurines are often announced to our email subscribers first, so be sure to sign up for exclusive access and updates on new arrivals.',
  },
]

export function FAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(null)

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Your Questions About Our Wood Carvings
          </h2>
          <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Have questions about our wood carved animals? We've got answers. Learn more about our process, materials, and the wood carving tools we use.
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto mb-12">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="border-b border-gray-200 last:border-b-0"
            >
              <button
                onClick={() => toggleFAQ(index)}
                className="w-full py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
              >
                <h3 className="text-lg md:text-xl font-semibold text-gray-900 pr-4">
                  {faq.question}
                </h3>
                <ChevronDown
                  className={`w-6 h-6 text-gray-500 transition-transform duration-200 ${
                    openIndex === index ? 'rotate-180' : ''
                  }`}
                />
              </button>
              
              <AnimatePresence>
                {openIndex === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="pb-6 pr-12">
                      <p className="text-gray-700 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Button 
            size="lg" 
            className="bg-amber-600 hover:bg-amber-700 text-white px-8 py-4 text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            Shop Our Wood Carved Animals Now
          </Button>
        </motion.div>
      </div>
    </section>
  )
}
