'use client'

import { motion } from 'framer-motion'

export function QuoteSection() {
  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center max-w-4xl mx-auto"
        >
          <blockquote className="text-2xl md:text-3xl lg:text-4xl font-light text-gray-700 italic leading-relaxed mb-8">
            "<PERSON> whispers the truth of nature in every grain, grounding us in a world ever drifting."
          </blockquote>
          <cite className="text-lg md:text-xl text-gray-600 font-medium">
            ~ K.C. Maeda ~
          </cite>
        </motion.div>
      </div>
    </section>
  )
}
