export interface MenuItem {
  label: string
  href: string
  children?: MenuItem[]
}
//
export const initMenu: MenuItem[] = [
  {
    label: '首页',
    href: '/',
  },
  {
    label: '手工木雕',
    href: '/woodcarving',
    children: [
      {
        href: '/woodcarving/animal',
        label: '动物',
        children: [
          {
            href: '/woodcarving/animal/dog',
            label: '狗',
          },
          {
            href: '/woodcarving/animal/cat',
            label: '猫',
          },
          {
            href: '/woodcarving/animal/bear',
            label: '熊',
          },
          {
            href: '/woodcarving/animal/fox',
            label: '狐狸',
          },
          {
            href: '/woodcarving/animal/raccoon',
            label: '浣熊',
          },
          {
            href: '/woodcarving/animal/chicken',
            label: '鸡',
          },
          {
            href: '/woodcarving/animal/duck',
            label: '鸭',
          },
          {
            href: '/woodcarving/animal/deer',
            label: '鹿',
          },
          {
            href: '/woodcarving/animal/hedgehog',
            label: '刺猬',
          },
          {
            href: '/woodcarving/animal/frog',
            label: '青蛙',
          },
          {
            href: '/woodcarving/animal/otter',
            label: '水獭',
          },
          {
            href: '/woodcarving/animal/squirrel',
            label: '松鼠',
          },
          {
            href: '/woodcarving/animal/rabbit',
            label: '兔',
          },
          {
            href: '/woodcarving/animal/pig',
            label: '猪',
          },
        ],
      },
      {
        href: '/woodcarving/type/2',
        label: '挂件',
      },
      {
        href: '/woodcarving/type/3',
        label: '海洋',
        children: [
          {
            label: '冰箱贴',
            href: '/woodcarving/type/3/1',
          },
          {
            label: '鲸鱼',
            href: '/woodcarving/type/3/2',
          },
          {
            label: '企鹅',
            href: '/woodcarving/type/3/3',
          },
        ],
      },
      {
        label: '昆虫',
        href: '/woodcarving/type/4',
      },
      {
        label: '鸟类',
        href: '/woodcarving/type/5',
      },
      {
        label: '人物',
        href: '/woodcarving/type/6',
      },
      {
        label: '植物',
        href: '/woodcarving/type/7',
      },
      {
        label: '木雕组合',
        href: '/woodcarving/type/8',
      },
      {
        label: '其他',
        href: '/woodcarving/other',
      },
    ],
  },
  {
    label: '生活家居',
    href: '/living-home',
    children: [
      {
        label: '手机支架',
        href: '/living-home/mobile-holder',
      },
      {
        label: '音乐盒',
        href: '/living-home/music-box',
      },
      {
        label: '装饰',
        href: '/living-home/decorate',
        children: [
          {
            label: '摆件',
            href: '/living-home/decorate/ornaments',
          },
        ],
      },
      {
        label: '其他',
        href: '/living-home/other',
      },
    ],
  },
  {
    label: '畅销',
    href: '/bestseller',
  },
  {
    label: '最新上新',
    href: '/latest-new-arrivals',
  },
  {
    label: 'About Us',
    href: '/about-us',
    children: [
      {
        label: 'Brand Story',
        href: '/brand-story',
      },
      {
        label: 'Contact',
        href: '/about-us/contact',
      },
      {
        label: 'All Designers',
        href: '/about-us/all-designers',
      },
      {
        label: 'Blog',
        href: '/blog',
      },
    ],
  },
]

export const initFootMenu: MenuItem[] = [
  {
    label: 'About',
    href: '/',
    children: [
      {
        label: 'Home Page',
        href: '/',
      },
      {
        label: 'Brand Story',
        href: '/',
      },
      {
        label: 'Contact',
        href: '/',
      },
      {
        label: 'ALl Designers',
        href: '/',
      },
      {
        label: 'Designer Interviews',
        href: '/',
      },
      {
        label: 'Our Kickstarter Journey',
        href: '/',
      },
      {
        label: 'Blog',
        href: '/blog',
      },
      {
        label: 'FAQ',
        href: '/',
      },
      {
        label: 'Terms and Conditions',
        href: '/',
      },
      {
        label: 'Privacy Policy',
        href: '/',
      },
      {
        label: 'Refund Policy',
        href: '/',
      },
      {
        label: 'Shipping Policy',
        href: '/',
      },
    ],
  },
  {
    label: 'Other Services',
    href: '/',
    children: [
      {
        label: 'Woodworking Course',
        href: '/',
      },
      {
        label: 'B2B Services',
        href: '/',
      },
      {
        label: 'Wholesale',
        href: '/',
      },
      {
        label: 'Product Customization',
        href: '/',
      },
      {
        label: 'Gift Card',
        href: '/',
      },
    ],
  },
]
