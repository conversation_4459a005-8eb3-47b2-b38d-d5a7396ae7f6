'use client'
import { useState, useEffect } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from '@ui/components/button'
import { cn } from '@ui/lib'

interface CarouselItem {
  id: number
  image: string
  title: string
  subtitle: string
  buttonText: string
  buttonLink: string
}

interface CarouselProps {
  items: CarouselItem[]
  autoPlay?: boolean
  autoPlayInterval?: number
  className?: string
}
export function Carousel({
  items,
  autoPlay = true,
  autoPlayInterval = 5000,
  className,
}: CarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  // 自动播放
  useEffect(() => {
    if (!autoPlay) return
    let timer: NodeJS.Timeout
    const interval = setInterval(() => {
      handleNext()
    }, autoPlayInterval)

    return () => {
      clearInterval(interval)
    }
  }, [currentIndex, autoPlay, autoPlayInterval])

  const handlePrevious = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? items.length - 1 : prevIndex - 1
    )
  }

  const handleNext = () => {
    setIsAnimating(true)
    setCurrentIndex((prevIndex) =>
      prevIndex === items.length - 1 ? 0 : prevIndex + 1
    )
    setTimeout(() => {
      setIsAnimating(false)
    }, 50)
  }

  const goToSlide = (index: number) => {
    setCurrentIndex(index)
  }

  if (!items.length) return null

  const currentItem = items[currentIndex]

  return (
    <div
      className={cn(
        'relative w-full h-[560px] md:h-[800px] overflow-hidden',
        className
      )}
    >
      {/* 背景图片 */}
      <div className="absolute inset-0">
        <div
          className={cn(
            'w-full h-full bg-cover bg-center bg-no-repeat transition-all duration-1000 ease-in-out',
            isAnimating ? 'scale-105 opacity-90' : 'scale-100 opacity-100'
          )}
          style={{
            backgroundImage: `url(${currentItem.image})`,
          }}
        />
        {/* 渐变遮罩层 */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/30 via-black/10 to-black/30" />
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10" />
      </div>

      {/* 导航箭头 */}
      <button
        onClick={handlePrevious}
        className="absolute left-2 md:left-4 lg:left-6 top-1/2 -translate-y-1/2 z-20 p-3 transition-all duration-300 text-white/60 hover:text-white hover:bg-white/10 rounded-full hover:scale-110"
        aria-label="Previous slide"
      >
        <ChevronLeft className="w-6 md:w-8 lg:w-12 h-6 md:h-8 lg:h-12" />
      </button>

      <button
        onClick={handleNext}
        className="absolute right-2 md:right-4 lg:right-6 top-1/2 -translate-y-1/2 z-20 p-3 transition-all duration-300 text-white/60 hover:text-white hover:bg-white/10 rounded-full hover:scale-110"
        aria-label="Next slide"
      >
        <ChevronRight className="w-6 md:w-8 lg:w-12 h-6 md:h-8 lg:h-12" />
      </button>

      {/* 指示器 */}
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 z-20 flex space-x-4">
        {items.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={cn(
              'transition-all duration-300 rounded-full',
              index === currentIndex
                ? 'w-8 h-3 bg-white shadow-lg'
                : 'w-3 h-3 bg-white/50 hover:bg-white/70 hover:scale-125'
            )}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  )
}

export function HeroCarousel() {
  const carouselItems: CarouselItem[] = [
    {
      id: 1,
      image: '/img-1.jpg',
      title: 'Craft • Wood • Nature',
      subtitle: 'Carved Wooden Sculpture',
      buttonText: 'SHOP NOW',
      buttonLink: '/shop',
    },
    {
      id: 2,
      image: '/img-1.jpg',
      title: 'Originality • Art • Fun',
      subtitle: 'Crafted Wooden Game & Toy',
      buttonText: 'SHOP NOW',
      buttonLink: '/shop',
    },
  ]

  return <Carousel items={carouselItems} />
}
