'use client'
import { useState, useEffect } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from '@ui/components/button'
import { cn } from '@ui/lib'

interface CarouselItem {
  id: number
  image: string
  title: string
  subtitle: string
  buttonText: string
  buttonLink: string
}

interface CarouselProps {
  items: CarouselItem[]
  autoPlay?: boolean
  autoPlayInterval?: number
  className?: string
}
export function Carousel({
  items,
  autoPlay = true,
  autoPlayInterval = 5000,
  className,
}: CarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  // 自动播放
  useEffect(() => {
    if (!autoPlay) return
    let timer: NodeJS.Timeout
    const interval = setInterval(() => {
      handleNext()
    }, autoPlayInterval)

    return () => {
      clearInterval(interval)
    }
  }, [currentIndex, autoPlay, autoPlayInterval])

  const handlePrevious = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? items.length - 1 : prevIndex - 1
    )
  }

  const handleNext = () => {
    setIsAnimating(false)
    setCurrentIndex((prevIndex) =>
      prevIndex === items.length - 1 ? 0 : prevIndex + 1
    )
    setTimeout(() => {
      setIsAnimating(true)
    }, 100)
  }

  const goToSlide = (index: number) => {
    setCurrentIndex(index)
  }

  if (!items.length) return null

  const currentItem = items[currentIndex]

  return (
    <div
      className={cn(
        'relative w-full h-[560px] md:h-[800px] overflow-hidden',
        className
      )}
    >
      {/* 背景图片 */}
      <div className="absolute inset-0">
        <div
          className={cn(
            'w-full h-full bg-cover bg-center bg-no-repeat transition-transform ease-in-out',
            !isAnimating ? 'scale-110' : 'scale-100'
          )}
          style={{
            backgroundImage: `url(${currentItem.image})`,
            transitionDuration: isAnimating ? '1s' : '0s',
          }}
        />
        {/* 遮罩层 */}
        <div className="absolute inset-0 bg-black/10" />
      </div>

      {/* 导航箭头 */}
      <button
        onClick={handlePrevious}
        className="absolute left-0.5 md:left-0.5 lg:left-6 top-1/2 -translate-y-1/2 z-20 p-3 transition-colors text-white/50 hover:text-white"
        aria-label="Previous slide"
      >
        <ChevronLeft className="w-6 md:w-6 lg:w-20 h-6 md:h-6 lg:h-20 " />
      </button>

      <button
        onClick={handleNext}
        className="absolute right-0.5 md:right-0.5 lg:right-6 top-1/2 -translate-y-1/2 z-20 p-3 transition-colors text-white/50 hover:text-white"
        aria-label="Next slide"
      >
        <ChevronRight className="w-6 md:w-6 lg:w-20 h-6 md:h-6 lg:h-20" />
      </button>

      {/* 指示器 */}
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 z-20 flex space-x-3">
        {items.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={cn(
              'w-3 h-3 rounded-full transition-all duration-300',
              index === currentIndex
                ? 'bg-white scale-125'
                : 'bg-white/50 hover:bg-white/70'
            )}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  )
}

export function HeroCarousel() {
  const carouselItems: CarouselItem[] = [
    {
      id: 1,
      image: '/img-1.jpg',
      title: 'Originality • Art • Fun',
      subtitle: 'Crafted Wooden Game & Toy',
      buttonText: 'SHOP NOW',
      buttonLink: '/shop',
    },
    {
      id: 2,
      image: '/img-2.jpg',
      title: 'Handcrafted Excellence',
      subtitle: 'Premium Quality Wooden Sculptures',
      buttonText: 'EXPLORE',
      buttonLink: '/carved-sculpture',
    },
  ]

  return <Carousel items={carouselItems} />
}
