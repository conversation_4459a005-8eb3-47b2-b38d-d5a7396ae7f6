'use client'

import { <PERSON> } from '@i18n/routing'
import { usePathname } from '@i18n/routing'
import { LocaleSwitch } from '@shared/components/LocaleSwitch'
import { Logo } from '@shared/components/Logo'
import { Button } from '@ui/components/button'
import { Sheet, SheetContent, SheetTrigger } from '@ui/components/sheet'
import { cn } from '@ui/lib'
import { Facebook, Instagram, MenuIcon, Twitter } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useEffect, useRef, useState } from 'react'
import { useDebounceCallback } from 'usehooks-ts'
import { getUserFromClientCookies } from '../../../../app/utils/client-cookies'
import Cookies from 'js-cookie'

import { User, LogOut, ChevronDown } from 'lucide-react'
import { analyticsService } from '@/services/analytics'
import { NewLocaleSwitch } from '@shared/components/NewLocalSwitch'
import { initMenu, MenuItem } from '@marketing/lib'

function UserMenu({ user, userInfoData }: any) {
  const [isOpen, setIsOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)
  const t = useTranslations()

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleLogout = () => {
    Cookies.remove('oauth_avatar')
    Cookies.remove('oauth_email')
    Cookies.remove('oauth_id')
    window.location.href = '/'
  }

  return (
    <div className="flex items-center gap-2">
      {/* <Button asChild variant='secondary' className='hidden md:flex'>
        <Link href='/app'>{t('common.menu.dashboard')}</Link>
      </Button> */}

      <div className="relative" ref={menuRef}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center gap-2 rounded-lg border border-border/40 bg-background/95 md:px-3 px-2 py-2 hover:bg-accent transition-colors duration-200"
        >
          <div className="flex items-center gap-2">
            <div className="flex-shrink-0">
              {user.avatar ? (
                <img
                  src={user.avatar}
                  alt={user.email}
                  className="md:h-8 md:w-8 rounded-full object-cover w-5 h-5"
                  style={{ aspectRatio: '1/1' }}
                />
              ) : (
                <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-primary/10 text-sm font-medium">
                  {user.email.substring(0, 1).toUpperCase()}
                </div>
              )}
            </div>
            <span className="hidden md:block text-sm font-medium">
              {user.email}
            </span>
          </div>
          <ChevronDown
            className={cn(
              'h-4 w-4 transition-transform duration-200',
              isOpen && 'rotate-180'
            )}
          />
        </button>

        {isOpen && (
          <div className="absolute right-0 mt-2 w-64 origin-top-right rounded-lg border border-border/40 shadow-lg animate-in fade-in-0 zoom-in-95 bg-black">
            <div className="px-4 py-3">
              <p className="text-sm font-medium">{user.email}</p>
              <p className="text-xs text-muted-foreground">
                {/* @ts-ignore */}
                {t('common.points')}: {userInfoData?.points}
              </p>
            </div>

            <div className="border-t border-border/40">
              <div className="flex flex-col py-2">
                {/* <Link
                  href='/app'
                  className='flex items-center px-4 py-2 text-sm hover:bg-accent transition-colors duration-200'
                  onClick={() => setIsOpen(false)}>
                  <LayoutDashboard className='mr-2 h-4 w-4' />
                  {t('common.menu.dashboard')}
                </Link> */}

                <Link
                  href="/auth/login"
                  className="flex items-center px-4 py-2 text-sm hover:bg-accent transition-colors duration-200"
                  onClick={() => setIsOpen(false)}
                >
                  <User className="mr-2 h-4 w-4" />
                  {t('common.menu.profile')}
                </Link>

                {/* <Link
                  href='/app/billing'
                  className='flex items-center px-4 py-2 text-sm hover:bg-accent transition-colors duration-200'
                  onClick={() => setIsOpen(false)}>
                  <CreditCard className='mr-2 h-4 w-4' />
                  {t('common.menu.billing')}
                </Link>

                <Link
                  href='/app/settings'
                  className='flex items-center px-4 py-2 text-sm hover:bg-accent transition-colors duration-200'
                  onClick={() => setIsOpen(false)}>
                  <Settings className='mr-2 h-4 w-4' />
                  {t('common.menu.settings')}
                </Link> */}
              </div>
            </div>

            <div className="border-t border-border/40 py-2">
              <button
                onClick={handleLogout}
                className="flex w-full items-center px-4 py-2 text-sm text-destructive hover:bg-accent transition-colors duration-200"
              >
                <LogOut className="mr-2 h-4 w-4" />
                {/* @ts-ignore */}
                {t('common.menu.logout')}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// 轮播文本数据
const promotionalTexts = [
  '所有木雕: 买2只85折; 买4只75折，',
  '全球配送服务& 100天退货保证',
]

export function Header() {
  const t = useTranslations()
  const [userInfoData, setUserInfoData] = useState<any>({})
  const user = getUserFromClientCookies()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const pathname = usePathname()
  const getUserInfo = async () => {
    if (user && user.email) {
      // @ts-ignore
      const userInfoResponse = await fetch(
        `/api/user/info?email=${encodeURIComponent(user.email)}`
      )
      const userInfoData = await userInfoResponse.json()
      if (userInfoData.success) {
        setUserInfoData(userInfoData.data)
        analyticsService.trackUserVisit(userInfoData.data.id)
      }
    }
  }
  const [menuItems, setMenuItems] = useState<MenuItem[]>(initMenu)
  // 当前悬浮的一级菜单 href
  const [hoveredMenu, setHoveredMenu] = useState<string | null>(null)
  // 找到当前悬浮菜单项的子菜单
  const activeMenu = menuItems.find(
    (item: MenuItem) => item.href === hoveredMenu
  )
  // 判断导航栏是否激活
  const isMenuItemActive = (href: string) => pathname.startsWith(href)
  // 当前轮播索引
  const [currentTextIndex, setCurrentTextIndex] = useState(0)
  // 是否滚动超出
  const [isScrollOut, setIsScrollOut] = useState(false)
  const [fade, setFade] = useState(false)
  const [isHeaderActive, setIsHeaderActive] = useState(false)
  // 移动端
  const [isMobile, setIsMobile] = useState(false)
  const [expandedMap, setExpandedMap] = useState<
    Record<number | string, boolean>
  >({}) // 控制每个二级菜单的展开状态
  const [isSecondMenu, setIsSecondMenu] = useState(false)
  const handleMouseEnter = (href: string) => {
    setHoveredMenu(href)
    setIsHeaderActive(true)
  }
  const handleMouseLeave = (href: string) => {
    if (!activeMenu?.children) {
      setIsHeaderActive(false)
      setHoveredMenu(null)
    }
  }
  // 移动端适配
  const [drawerOpen, setDrawerOpen] = useState(false)
  const [currentMenu, setCurrentMenu] = useState<MenuItem | null>(null)
  const handleClickMenu = (menu: MenuItem) => {
    if (menu.children) {
      if (isSecondMenu) {
        setExpandedMap((prev) => ({
          ...prev,
          [menu.href]: !prev[menu.href], // 切换当前菜单的展开状态
        }))
      } else {
        setCurrentMenu(menu)
        setMenuItems(menu.children) // 进入二级菜单
        setIsSecondMenu(true)
        setExpandedMap({}) // 重置展开状态
      }
    }
  }
  const handleDrawerBack = () => {
    setMenuItems(initMenu)
    setCurrentMenu(null)
    setIsSecondMenu(false)
    setExpandedMap({})
  }
  // 检测滚动高度
  const debouncedScrollHandler = useDebounceCallback(
    () => {
      const navHeight = 80 // 大概的导航栏高度
      setIsScrollOut(window.scrollY > navHeight)
    },
    150,
    {
      maxWait: 150,
    }
  )
  useEffect(() => {
    window.addEventListener('scroll', debouncedScrollHandler)
    debouncedScrollHandler()
    return () => {
      window.removeEventListener('scroll', debouncedScrollHandler)
    }
  }, [debouncedScrollHandler])
  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)

    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  useEffect(() => {
    setMobileMenuOpen(false)
    getUserInfo()
  }, [pathname])
  // 文本轮播
  useEffect(() => {
    // 页面加载时延迟 100ms 再淡入
    const firstTimeout = setTimeout(() => setFade(true), 100)
    const interval = setInterval(() => {
      setFade(false) // 先淡出
      setTimeout(() => {
        setCurrentTextIndex((prev) => (prev + 1) % promotionalTexts.length)
        setFade(true) // 再淡入
      }, 500) // 淡出持续 500ms
    }, 3000)

    return () => {
      clearTimeout(firstTimeout)
      clearInterval(interval)
    }
  }, [])

  return (
    <nav className="fixed top-0 left-0 z-50 w-full" data-test="navigation">
      {/* 宣传栏 */}
      {!isScrollOut && (
        <div className="bg-[#c4b39c] text-white py-[11px] text-sm md:block">
          <div className="mx-auto px-10 flex items-center justify-between">
            {/* 左侧社交媒体 */}
            {!isMobile ? (
              <div className="flex items-center gap-3 flex-grow-[2] flex-shrink-0 basis-0">
                <svg
                  className="w-[15px] h-[15px] cursor-pointer hover:opacity-70"
                  width="48px"
                  height="48px"
                  viewBox="0 0 48 48"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Facebook</title>
                  <g
                    stroke="none"
                    strokeWidth="1"
                    fill="none"
                    fillRule="evenodd"
                  >
                    <g
                      transform="translate(-325.000000, -295.000000)"
                      fill="currentColor"
                    >
                      <path d="M350.638355,343 L327.649232,343 C326.185673,343 325,341.813592 325,340.350603 L325,297.649211 C325,296.18585 326.185859,295 327.649232,295 L370.350955,295 C371.813955,295 373,296.18585 373,297.649211 L373,340.350603 C373,341.813778 371.813769,343 370.350955,343 L358.119305,343 L358.119305,324.411755 L364.358521,324.411755 L365.292755,317.167586 L358.119305,317.167586 L358.119305,312.542641 C358.119305,310.445287 358.701712,309.01601 361.70929,309.01601 L365.545311,309.014333 L365.545311,302.535091 C364.881886,302.446808 362.604784,302.24957 359.955552,302.24957 C354.424834,302.24957 350.638355,305.625526 350.638355,311.825209 L350.638355,317.167586 L344.383122,317.167586 L344.383122,324.411755 L350.638355,324.411755 L350.638355,343 L350.638355,343 Z"></path>
                    </g>
                  </g>
                </svg>
                <svg
                  className="w-[15px] h-[15px] cursor-pointer hover:opacity-70"
                  width="48px"
                  height="48px"
                  viewBox="0 0 48 48"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Instagram</title>
                  <defs></defs>
                  <g
                    stroke="none"
                    strokeWidth="1"
                    fill="none"
                    fillRule="evenodd"
                  >
                    <g
                      transform="translate(-642.000000, -295.000000)"
                      fill="currentColor"
                    >
                      <path d="M666.000048,295 C659.481991,295 658.664686,295.027628 656.104831,295.144427 C653.550311,295.260939 651.805665,295.666687 650.279088,296.260017 C648.700876,296.873258 647.362454,297.693897 646.028128,299.028128 C644.693897,300.362454 643.873258,301.700876 643.260017,303.279088 C642.666687,304.805665 642.260939,306.550311 642.144427,309.104831 C642.027628,311.664686 642,312.481991 642,319.000048 C642,325.518009 642.027628,326.335314 642.144427,328.895169 C642.260939,331.449689 642.666687,333.194335 643.260017,334.720912 C643.873258,336.299124 644.693897,337.637546 646.028128,338.971872 C647.362454,340.306103 648.700876,341.126742 650.279088,341.740079 C651.805665,342.333313 653.550311,342.739061 656.104831,342.855573 C658.664686,342.972372 659.481991,343 666.000048,343 C672.518009,343 673.335314,342.972372 675.895169,342.855573 C678.449689,342.739061 680.194335,342.333313 681.720912,341.740079 C683.299124,341.126742 684.637546,340.306103 685.971872,338.971872 C687.306103,337.637546 688.126742,336.299124 688.740079,334.720912 C689.333313,333.194335 689.739061,331.449689 689.855573,328.895169 C689.972372,326.335314 690,325.518009 690,319.000048 C690,312.481991 689.972372,311.664686 689.855573,309.104831 C689.739061,306.550311 689.333313,304.805665 688.740079,303.279088 C688.126742,301.700876 687.306103,300.362454 685.971872,299.028128 C684.637546,297.693897 683.299124,296.873258 681.720912,296.260017 C680.194335,295.666687 678.449689,295.260939 675.895169,295.144427 C673.335314,295.027628 672.518009,295 666.000048,295 Z M666.000048,299.324317 C672.40826,299.324317 673.167356,299.348801 675.69806,299.464266 C678.038036,299.570966 679.308818,299.961946 680.154513,300.290621 C681.274771,300.725997 682.074262,301.246066 682.91405,302.08595 C683.753934,302.925738 684.274003,303.725229 684.709379,304.845487 C685.038054,305.691182 685.429034,306.961964 685.535734,309.30194 C685.651199,311.832644 685.675683,312.59174 685.675683,319.000048 C685.675683,325.40826 685.651199,326.167356 685.535734,328.69806 C685.429034,331.038036 685.038054,332.308818 684.709379,333.154513 C684.274003,334.274771 683.753934,335.074262 682.91405,335.91405 C682.074262,336.753934 681.274771,337.274003 680.154513,337.709379 C679.308818,338.038054 678.038036,338.429034 675.69806,338.535734 C673.167737,338.651199 672.408736,338.675683 666.000048,338.675683 C659.591264,338.675683 658.832358,338.651199 656.30194,338.535734 C653.961964,338.429034 652.691182,338.038054 651.845487,337.709379 C650.725229,337.274003 649.925738,336.753934 649.08595,335.91405 C648.246161,335.074262 647.725997,334.274771 647.290621,333.154513 C646.961946,332.308818 646.570966,331.038036 646.464266,328.69806 C646.348801,326.167356 646.324317,325.40826 646.324317,319.000048 C646.324317,312.59174 646.348801,311.832644 646.464266,309.30194 C646.570966,306.961964 646.961946,305.691182 647.290621,304.845487 C647.725997,303.725229 648.246066,302.925738 649.08595,302.08595 C649.925738,301.246066 650.725229,300.725997 651.845487,300.290621 C652.691182,299.961946 653.961964,299.570966 656.30194,299.464266 C658.832644,299.348801 659.59174,299.324317 666.000048,299.324317 Z M666.000048,306.675683 C659.193424,306.675683 653.675683,312.193424 653.675683,319.000048 C653.675683,325.806576 659.193424,331.324317 666.000048,331.324317 C672.806576,331.324317 678.324317,325.806576 678.324317,319.000048 C678.324317,312.193424 672.806576,306.675683 666.000048,306.675683 Z M666.000048,327 C661.581701,327 658,323.418299 658,319.000048 C658,314.581701 661.581701,311 666.000048,311 C670.418299,311 674,314.581701 674,319.000048 C674,323.418299 670.418299,327 666.000048,327 Z M681.691284,306.188768 C681.691284,307.779365 680.401829,309.068724 678.811232,309.068724 C677.22073,309.068724 675.931276,307.779365 675.931276,306.188768 C675.931276,304.598171 677.22073,303.308716 678.811232,303.308716 C680.401829,303.308716 681.691284,304.598171 681.691284,306.188768 Z"></path>
                    </g>
                  </g>
                </svg>
                <svg
                  className="w-[15px] h-[15px] cursor-pointer hover:opacity-70"
                  width="48px"
                  height="34px"
                  viewBox="0 0 48 34"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Youtube</title>
                  <g
                    stroke="none"
                    strokeWidth="1"
                    fill="none"
                    fillRule="evenodd"
                  >
                    <g
                      transform="translate(-567.000000, -302.000000)"
                      fill="currentColor"
                    >
                      <path d="M586.044,325.269916 L586.0425,311.687742 L599.0115,318.502244 L586.044,325.269916 Z M614.52,309.334163 C614.52,309.334163 614.0505,306.003199 612.612,304.536366 C610.7865,302.610299 608.7405,302.601235 607.803,302.489448 C601.086,302 591.0105,302 591.0105,302 L590.9895,302 C590.9895,302 580.914,302 574.197,302.489448 C573.258,302.601235 571.2135,302.610299 569.3865,304.536366 C567.948,306.003199 567.48,309.334163 567.48,309.334163 C567.48,309.334163 567,313.246723 567,317.157773 L567,320.82561 C567,324.73817 567.48,328.64922 567.48,328.64922 C567.48,328.64922 567.948,331.980184 569.3865,333.447016 C571.2135,335.373084 573.612,335.312658 574.68,335.513574 C578.52,335.885191 591,336 591,336 C591,336 601.086,335.984894 607.803,335.495446 C608.7405,335.382148 610.7865,335.373084 612.612,333.447016 C614.0505,331.980184 614.52,328.64922 614.52,328.64922 C614.52,328.64922 615,324.73817 615,320.82561 L615,317.157773 C615,313.246723 614.52,309.334163 614.52,309.334163 L614.52,309.334163 Z"></path>
                    </g>
                  </g>
                </svg>
                <svg
                  className="w-[15px] h-[15px] cursor-pointer hover:opacity-70"
                  width="48px"
                  height="48px"
                  viewBox="0 0 48 48"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Pinterest</title>
                  <g
                    stroke="none"
                    strokeWidth="1"
                    fill="none"
                    fillRule="evenodd"
                  >
                    <g
                      transform="translate(-407.000000, -295.000000)"
                      fill="currentColor"
                    >
                      <path d="M431.001411,295 C417.747575,295 407,305.744752 407,319.001411 C407,328.826072 412.910037,337.270594 421.368672,340.982007 C421.300935,339.308344 421.357382,337.293173 421.78356,335.469924 C422.246428,333.522491 424.871229,322.393897 424.871229,322.393897 C424.871229,322.393897 424.106368,320.861351 424.106368,318.59499 C424.106368,315.038808 426.169518,312.38296 428.73505,312.38296 C430.91674,312.38296 431.972306,314.022755 431.972306,315.987123 C431.972306,318.180102 430.572411,321.462515 429.852708,324.502205 C429.251543,327.050803 431.128418,329.125243 433.640325,329.125243 C438.187158,329.125243 441.249427,323.285765 441.249427,316.36532 C441.249427,311.10725 437.707356,307.170048 431.263891,307.170048 C423.985006,307.170048 419.449462,312.59746 419.449462,318.659905 C419.449462,320.754101 420.064738,322.227377 421.029988,323.367613 C421.475922,323.895396 421.535191,324.104251 421.374316,324.708238 C421.261422,325.145705 420.996119,326.21256 420.886047,326.633092 C420.725172,327.239901 420.23408,327.460046 419.686541,327.234256 C416.330746,325.865408 414.769977,322.193509 414.769977,318.064385 C414.769977,311.248368 420.519139,303.069148 431.921503,303.069148 C441.085729,303.069148 447.117128,309.704533 447.117128,316.819721 C447.117128,326.235138 441.884459,333.268478 434.165285,333.268478 C431.577174,333.268478 429.138649,331.868584 428.303228,330.279591 C428.303228,330.279591 426.908979,335.808608 426.615452,336.875463 C426.107426,338.724114 425.111131,340.575587 424.199506,342.014994 C426.358617,342.652849 428.63909,343 431.001411,343 C444.255248,343 455,332.255248 455,319.001411 C455,305.744752 444.255248,295 431.001411,295"></path>
                    </g>
                  </g>
                </svg>
              </div>
            ) : (
              <div></div>
            )}
            {/*  中间轮播文本 */}
            <div className="text-center lg:truncate max-w-[50%] flex-grow flex-shrink basis-auto">
              <div className="relative overflow-hidden h-5">
                <strong
                  className={`transition-opacity duration-500 ease-in-out ${
                    fade ? 'opacity-100' : 'opacity-0'
                  }`}
                >
                  {promotionalTexts[currentTextIndex]}
                </strong>
              </div>
            </div>
            {/* 右侧国际化 */}
            {!isMobile ? (
              <div className="flex-grow-[2] flex-shrink-0 basis-0 text-end flex justify-end"></div>
            ) : (
              <div></div>
            )}
          </div>
        </div>
      )}
      {/*  PC端菜单 */}
      <div
        className={cn(
          'hidden md:flex w-full items-center transition-[padding] duration-200 bg-transparent',
          isHeaderActive || isScrollOut
            ? 'border-b-[1px] border-[#e2e2e2] bg-white'
            : ''
        )}
      >
        <div className={cn(`w-full items-center flex gap-6 px-10`)}>
          {/* 左侧：导航菜单 */}
          <div className="flex items-center flex-grow-[2] flex-shrink-0 basis-0 -mx-[18px]">
            {/* 只要第一层 */}
            {initMenu.map((menuItem: MenuItem) => (
              <Link
                href={menuItem.href}
                className={cn(
                  'text-sm relative p-[18px] flex justify-start items-center',
                  activeMenu?.href === menuItem.href && 'text-black'
                )}
                key={menuItem.href}
                onMouseEnter={() => handleMouseEnter(menuItem.href)}
                onMouseLeave={() => handleMouseLeave(menuItem.href)}
              >
                <div
                  className={cn(
                    'relative before:absolute before:left-1/2 before:bottom-[calc(100%-46px)] before:h-[2px] before:w-full before:origin-center before:scale-x-0 before:transition-transform before:duration-200 hover:before:scale-x-100 before:-translate-x-1/2',
                    isHeaderActive || isScrollOut
                      ? 'text-black before:bg-black'
                      : 'text-white before:bg-white',
                    isMenuItemActive(menuItem.href) ||
                      hoveredMenu === menuItem.href
                      ? 'before:scale-x-100'
                      : ''
                  )}
                >
                  {menuItem.label}
                </div>
              </Link>
            ))}
          </div>
          {/* 中间：Logo */}
          <div className="flex items-center justify-center gap-4 flex-grow-[1] flex-shrink-[1] basis-auto max-w-[250px]">
            <Link href="/" className="text-sm p-4">
              <Logo withLabel />
            </Link>
          </div>
          {/* 右侧：用户操作区域 */}
          <div className="flex items-center justify-end gap-3 flex-grow-[2] flex-shrink-0 basis-0">
            <Sheet
              open={mobileMenuOpen}
              onOpenChange={(open) => setMobileMenuOpen(open)}
            >
              <SheetTrigger asChild>
                <Button
                  className="md:hidden"
                  size="icon"
                  variant="outline"
                  aria-label="Menu"
                >
                  <MenuIcon className="size-4" />
                </Button>
              </SheetTrigger>
              <SheetContent className="w-[250px]" side="right">
                <div className="flex flex-col items-start justify-center">
                  {/* Add Tools section for mobile */}
                  <div className="w-full">
                    <div className="px-3 py-2 text-base font-semibold text-foreground">
                      {t('menu.tools')}
                    </div>
                    <div className="pl-6">
                      <Link
                        href="/ai-lyrics-generator"
                        className="block px-3 py-2 text-sm text-foreground/80"
                      >
                        {t('menu.aiLyricsGenerator')}
                      </Link>
                      <Link
                        href="/ai-vocal-remover"
                        className="block px-3 py-2 text-sm text-foreground/80"
                      >
                        {t('menu.vocalRemover')}
                      </Link>
                      <Link
                        href="/ai-song-cover"
                        className="block px-3 py-2 text-sm text-foreground/80"
                      >
                        {t('menu.songCover')}
                      </Link>
                    </div>
                  </div>

                  {menuItems.map((menuItem: MenuItem) => (
                    <Link
                      key={menuItem.href}
                      href={menuItem.href}
                      className={cn(
                        'block px-3 py-2 font-medium text-base text-foreground/80',
                        isMenuItemActive(menuItem.href)
                          ? 'font-bold text-foreground'
                          : ''
                      )}
                    >
                      {menuItem.label}
                    </Link>
                  ))}

                  <Link
                    key={user ? 'dashboard' : 'login'}
                    href={user ? '/app' : '/auth/login'}
                    className="block px-3 py-2 text-base"
                    prefetch={!user}
                  >
                    {user ? t('common.menu.dashboard') : t('common.menu.login')}
                  </Link>
                </div>
              </SheetContent>
            </Sheet>

            {!isMobile && <LocaleSwitch />}
            {user ? (
              <UserMenu user={user} userInfoData={userInfoData} />
            ) : (
              <Button
                key="login"
                className="hidden md:flex"
                asChild
                variant="secondary"
              >
                <Link href="/auth/login" className="min-w-[max-content]">
                  {t('common.menu.login')}
                </Link>
              </Button>
            )}
          </div>
        </div>
      </div>
      {/* PC下拉菜单 */}
      <div
        onMouseLeave={() => {
          setIsHeaderActive(false)
          setHoveredMenu(null)
        }}
        className={cn(
          'w-full bg-white text-[#676869] transition-all duration-200 overflow-auto max-h-[calc(100vh-137px-100px)]',
          isHeaderActive ? 'border-b-[1px] border-[#e2e2e2]' : 'border-none'
        )}
      >
        <div className="px-10 w-full">
          {activeMenu && activeMenu.children && (
            <div className="pt-8 pb-5 flex-wrap max-w-[1200px] mx-auto">
              <div className="flex items-start justify-center overflow-auto -mx-[-15px]">
                {activeMenu?.children?.map((child: MenuItem) => (
                  <div
                    className="flex-grow flex-shrink-0 basis-auto text-center h-full"
                    key={child.href}
                  >
                    <Link
                      href={child.href}
                      className="text-sm hover:text-black"
                    >
                      {child.label}
                    </Link>
                    {child.children?.map((child: MenuItem) => (
                      <div
                        className="flex flex-wrap flex-col gap-1 mt-2"
                        key={child.href}
                      >
                        <Link
                          href={child.href}
                          className="hover:text-black text-sm"
                        >
                          {child.label}
                        </Link>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 移动端菜单 */}
      <div
        className={cn(
          'md:hidden z-4000 text-[#171717] transition-all ease-in duration-150'
        )}
      >
        <div
          className={cn(
            'flex items-center justify-between border-b-[#e2e2e2] relative text-sm p-2 z-5000',
            isHeaderActive || isScrollOut
              ? 'border-b-[1px] border-[#e2e2e2] bg-white'
              : ''
          )}
        >
          {/* 按钮 */}
          <div className="flex-grow-[2] flex-shrink-0 basis-0 flex items-center">
            <button className="md:hidden" onClick={() => setDrawerOpen(true)}>
              {/* <MenuIcon className="h-6 w-6 " /> */}
              <svg
                className="w-6 h-6"
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke={isHeaderActive || isScrollOut ? 'black' : 'white'}
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                aria-hidden="true"
              >
                <line x1="3" y1="12" x2="21" y2="12"></line>
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <line x1="3" y1="18" x2="21" y2="18"></line>
              </svg>
            </button>
          </div>
          {/* logo */}
          <div className="max-w-[150px] flex-grow-[1] flex-shrink-[1] basis-auto text-center">
            <Link href="/" className="text-sm">
              <Logo withLabel className="text-black" />
            </Link>
          </div>
          {/* 登录 */}
          <div className="flex-grow-[2] flex-shrink-0 basis-0 flex items-center justify-end">
            {user ? (
              <UserMenu user={user} userInfoData={userInfoData} />
            ) : (
              <Button key="login" asChild variant="secondary" size="sm">
                <Link href="/auth/login">{t('common.menu.login')}</Link>
              </Button>
            )}
          </div>
        </div>
      </div>
      {/* 移动端抽屉 */}
      <>
        <div
          className={cn(
            'fixed inset-0 bg-black/50 z-50 transition-opacity',
            drawerOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
          )}
          onClick={() => setDrawerOpen(false)}
        ></div>

        <div
          className={cn(
            'fixed top-0 left-0 h-full w-[80%] bg-white z-50 transform transition-transform text-[#676869] overflow-auto',
            drawerOpen ? 'translate-x-0' : '-translate-x-full'
          )}
        >
          {/* 头部 */}
          <div className="flex justify-between items-start p-3 border-b border-b-[#e2e2e2]">
            {/* 返回按钮 */}
            {currentMenu?.children && (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.3"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="stroke-[rgb(103,104,105)]"
                onClick={handleDrawerBack}
              >
                <title>左</title>
                <polyline points="15 18 9 12 15 6"></polyline>
              </svg>
            )}
            {/* 菜单标题 */}
            <div className="text-ellipsis">{currentMenu?.label || ''}</div>
            {/* 关闭按钮 */}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.3"
              strokeLinecap="round"
              strokeLinejoin="round"
              aria-hidden="true"
              className="stroke-[rgb(103,104,105)]"
              onClick={() => setDrawerOpen(false)}
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </div>
          {/* 菜单栏 */}
          <div className="text-start py-3 transition-all ease-in duration-100">
            {menuItems.map((menuItem: MenuItem) => {
              return (
                <div
                  className="flex items-start justify-between flex-wrap z-10"
                  key={menuItem.href}
                  onClick={() => handleClickMenu(menuItem)}
                >
                  {menuItem.children ? (
                    <div className="px-5 py-3">{menuItem.label}</div>
                  ) : (
                    <Link href={menuItem.href} className="px-5 py-3">
                      {menuItem.label}
                    </Link>
                  )}
                  {menuItem.children && !isSecondMenu && (
                    <div className="flex items-center justify-center py-2 px-5">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="1.3"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="stroke-[rgb(103,104,105)] -rotate-90"
                      >
                        <title>切換菜單</title>
                        <polyline points="6 9 12 15 18 9"></polyline>
                      </svg>
                    </div>
                  )}
                  {menuItem.children && isSecondMenu && (
                    <div className="flex items-center justify-center py-2 px-5">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="1.3"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className={cn(
                          'stroke-[rgb(103,104,105)]',
                          expandedMap[menuItem.href] ? 'rotate-180' : ''
                        )}
                      >
                        <title>切換菜單</title>
                        <polyline points="6 9 12 15 18 9"></polyline>
                      </svg>
                    </div>
                  )}
                  {menuItem.children && isSecondMenu && (
                    <div
                      className={cn(
                        'transition-all duration-100 opacity-0 h-0 z-0 w-full pointer-events-none',
                        expandedMap[menuItem.href] &&
                          'visible opacity-100 h-full pointer-events-auto'
                      )}
                    >
                      <ul className="list-none">
                        {menuItem?.children.map((menu: MenuItem) => {
                          return (
                            <li
                              className="list-none items-start flex"
                              key={menu.href}
                            >
                              <Link
                                href={menu.href}
                                className="px-5 py-3 text-inherit text-start"
                              >
                                {menu.label}
                              </Link>
                            </li>
                          )
                        })}
                      </ul>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </div>
      </>
    </nav>
  )
}
