'use client'

import { MDXContent } from '@content-collections/mdx/react'
import { mdxComponents } from '../utils/mdx-components'
import React, { createContext, useRef } from 'react'

// 新增：定义SlugContext
export const SlugContext = createContext<string[] | null>(null)
export const SlugIndexContext = createContext<{ current: number }>({
  current: 0,
})

export function PostContent({ content }: { content: string }) {
  return (
    <div className="prose dark:prose-invert mx-auto mt-6 max-w-5xl">
      <MDXContent code={content} components={mdxComponents} />
    </div>
  )
}
