const translations: Record<string, any> = {
  en: {
    emailTemplate: {
      greeting: 'Hello, ',
      verificationCodeRequest:
        "You've requested a verification code for your removeai account. Please use the code below to complete your action:",
      codeExpiration:
        "This code will expire in 5 minutes. If you didn't request this code, please ignore this email or contact us if you have concerns.",
      regards: 'Best regards,',
      team: 'The removeai Team',
      rights: 'All rights reserved.',
      support: 'Contact Support',
      privacy: 'Privacy Policy',
      autoMessage:
        'This is an automated message, please do not reply directly to this email.',
    },
  },
  'zh-CN': {
    emailTemplate: {
      greeting: '您好，',
      verificationCodeRequest:
        '您已请求获取用于您的 removeai 账户的验证码。请使用以下验证码来完成您的操作：',
      codeExpiration:
        '此验证码将在 5 分钟后过期。如果您并未请求此验证码，请忽略此电子邮件，或者如果您有疑虑，请与我们联系。',
      regards: '致以最诚挚的问候，',
      team: 'removeai 团队',
      rights: '保留所有权利。',
      support: '联系支持团队',
      privacy: '隐私政策',
      autoMessage: '这是一条自动发送的消息，请不要直接回复此电子邮件。',
    },
  },
  'zh-HK': {
    emailTemplate: {
      greeting: '你好，',
      verificationCodeRequest:
        '你已請求獲取用於你嘅 removeai 帳戶嘅驗證碼。請使用以下驗證碼嚟完成你嘅操作：',
      codeExpiration:
        '此驗證碼將喺 5 分鐘後過期。如果你並未請求此驗證碼，請忽略呢封電子郵件，或者如果你有疑慮，請與我哋聯繫。',
      regards: '致以最誠摯嘅問候，',
      team: 'removeai 團隊',
      rights: '保留所有權利。',
      support: '聯絡支援團隊',
      privacy: '私隱政策',
      autoMessage: '呢係一條自動發送嘅消息，請唔好直接回覆呢封電子郵件。',
    },
  },
  'zh-TW': {
    emailTemplate: {
      greeting: '您好，',
      verificationCodeRequest:
        '您已請求取得用於您的 removeai 帳戶的驗證碼。請使用以下驗證碼來完成您的操作：',
      codeExpiration:
        '此驗證碼將在 5 分鐘後過期。如果您並未請求此驗證碼，請忽略這封電子郵件，或者如果您有疑慮，請與我們聯繫。',
      regards: '致以最誠摯的問候，',
      team: 'removeai 團隊',
      rights: '保留所有權利。',
      support: '聯繫支援團隊',
      privacy: '隱私政策',
      autoMessage: '這是一條自動發送的消息，請不要直接回覆此電子郵件。',
    },
  },
  ja: {
    emailTemplate: {
      greeting: 'こんにちは、',
      verificationCodeRequest:
        'あなたはremoveaiアカウント用の認証コードを要求しました。以下のコードを使用して、操作を完了してください：',
      codeExpiration:
        'このコードは5分後に有効期限が切れます。もしあなたがこのコードを要求していない場合は、このメールを無視してください。もし心配事があれば、私たちに連絡してください。',
      regards: '敬具',
      team: 'removeaiチーム',
      rights: 'すべての権利を留保します。',
      support: 'サポートに連絡する',
      privacy: 'プライバシーポリシー',
      autoMessage:
        'これは自動送信メッセージです。直接このメールに返信しないでください。',
    },
  },
  ko: {
    emailTemplate: {
      greeting: '안녕하세요, ',
      verificationCodeRequest:
        '당신은 removeai 계정에 대한 인증 코드를 요청했습니다. 아래 코드를 사용하여 작업을 완료하세요:',
      codeExpiration:
        '이 코드는 5분 후 만료됩니다. 만약 이 코드를 요청하지 않았다면, 이 이메일을 무시하시거나 궁금한 사항이 있으면 연락해 주세요.',
      regards: '좋은 하루 보내세요,',
      team: 'removeai 팀',
      rights: '모든 권리를 보유합니다.',
      support: '지원 팀에 문의',
      privacy: '개인 정보 보호 정책',
      autoMessage:
        '이것은 자동 발송 메시지입니다. 직접 이 이메일에 회신하지 마세요.',
    },
  },
  es: {
    emailTemplate: {
      greeting: 'Hola, ',
      verificationCodeRequest:
        'Ha solicitado un código de verificación para su cuenta de removeai. Por favor, utilice el código a continuación para completar su acción:',
      codeExpiration:
        'Este código caducará en 5 minutos. Si no solicitó este código, por favor ignore este correo electrónico o contáctenos si tiene preguntas.',
      regards: 'Saludos cordiales,',
      team: 'El Equipo de removeai',
      rights: 'Todos los derechos reservados.',
      support: 'Contactar al Soporte',
      privacy: 'Política de Privacidad',
      autoMessage:
        'Este es un mensaje automático, por favor no responda directamente a este correo electrónico.',
    },
  },
  de: {
    emailTemplate: {
      greeting: 'Hallo, ',
      verificationCodeRequest:
        'Sie haben einen Verifizierungscode für Ihr removeai-Konto angefordert. Bitte verwenden Sie den folgenden Code, um Ihre Aktion abzuschließen:',
      codeExpiration:
        'Dieser Code wird in 5 Minuten ablaufen. Wenn Sie diesen Code nicht angefordert haben, ignorieren Sie bitte diese E-Mail oder kontaktieren Sie uns, wenn Sie Bedenken haben.',
      regards: 'Mit freundlichen Grüßen,',
      team: 'Das removeai-Team',
      rights: 'Alle Rechte vorbehalten.',
      support: 'Support kontaktieren',
      privacy: 'Datenschutzrichtlinie',
      autoMessage:
        'Dies ist eine automatisch generierte Nachricht. Bitte antworten Sie nicht direkt auf diese E-Mail.',
    },
  },
  fr: {
    emailTemplate: {
      greeting: 'Bonjour, ',
      verificationCodeRequest:
        'Vous avez demandé un code de vérification pour votre compte removeai. Veuillez utiliser le code ci-dessous pour compléter votre action:',
      codeExpiration:
        "Ce code expirera dans 5 minutes. Si vous n'avez pas demandé ce code, veuillez ignorer cet e-mail ou nous contacter si vous avez des questions.",
      regards: 'Cordialement,',
      team: "L'Équipe removeai",
      rights: 'Tous droits réservés.',
      support: 'Contactez le Support',
      privacy: 'Politique de confidentialité',
      autoMessage:
        'Ceci est un message automatique, veuillez ne pas répondre directement à cet e-mail.',
    },
  },
  pt: {
    emailTemplate: {
      greeting: 'Olá, ',
      verificationCodeRequest:
        'Você solicitou um código de verificação para sua conta do removeai. Por favor, use o código abaixo para concluir sua ação:',
      codeExpiration:
        'Este código expirará em 5 minutos. Se você não solicitou esse código, por favor, ignore este e-mail ou entre em contato conosco se tiver dúvidas.',
      regards: 'Atenciosamente,',
      team: 'A Equipe do removeai',
      rights: 'Todos os direitos reservados.',
      support: 'Contatar o Suporte',
      privacy: 'Política de Privacidade',
      autoMessage:
        'Esta é uma mensagem automática, por favor não responda diretamente a este e-mail.',
    },
  },
  ru: {
    emailTemplate: {
      greeting: 'Привет, ',
      verificationCodeRequest:
        'Вы запросили код подтверждения для вашего аккаунта removeai. Пожалуйста, используйте следующий код, чтобы выполнить ваше действие:',
      codeExpiration:
        ' Этот код истекает через 5 минут. Если вы не запрашивали этот код, проигнорируйте это письмо или свяжитесь с нами, если есть какие-либо сомнения.',
      regards: 'С наилучшими пожеланиями,',
      team: 'Команда removeai',
      rights: 'Все права защищены.',
      support: 'Связаться с Поддержкой',
      privacy: 'Политика конфиденциальности',
      autoMessage:
        'Это автоматическое сообщение, пожалуйста, не отвечайте напрямую на это письмо.',
    },
  },
  th: {
    emailTemplate: {
      greeting: 'สวัสดี, ',
      verificationCodeRequest:
        'คุณได้ขอรหัสยืนยันสำหรับบัญชี removeai ของคุณ โปรดใช้รหัสต่อไปนี้เพื่อให้การดำเนินการของคุณสำเร็จ:',
      codeExpiration:
        'รหัสนี้จะหมดอายุในช่วง 5 นาที ถ้าคุณไม่ได้ขอรหัสนี้ โปรดละทิ้งอีเมลนี้หรือติดต่อเรา якщоคุณมีความคิดสงสัย.',
      regards: 'ทดแทนคำถามดี,',
      team: 'ทีม removeai',
      rights: 'สิทธิทั้งหมดถูกถวง/',
      support: 'ติดต่อทีมSupport',
      privacy: 'นโยบายความเป็นส่วนตัว',
      autoMessage:
        'นี่คือข้อความที่ส่งอัตโนมัติ โปรดอย่าردิ坲ข้อความอีเมลนี้โดยตรง',
    },
  },
  vi: {
    emailTemplate: {
      greeting: 'Xin chào, ',
      verificationCodeRequest:
        'Bạn đã yêu cầu mã xác minh cho tài khoản removeai của bạn. Vui lòng sử dụng mã bên dưới để hoàn thành hành động của bạn:',
      codeExpiration:
        'Mã này sẽ hết hạn trong 5 phút. Nếu bạn không yêu cầu mã này, vui lòng bỏ qua email này hoặc liên hệ với chúng tôi nếu bạn có những câu hỏi.',
      regards: 'Trân trọng,',
      team: 'Đội Ngũ removeai',
      rights: 'Tất cả các quyền được bảo lưu.',
      support: 'Liên Hệ Hỗ Trợ',
      privacy: 'Chính Sách Bảo Mật',
      autoMessage:
        'Đây là một tin nhắn tự động, vui lòng không trả lời trực tiếp email này.',
    },
  },
}

export default function generateVerificationEmail(
  verificationCode: string,
  locale = 'en'
) {
  const t = translations[locale] || translations.en

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Your removeai Verification Code</title>
      <style>
        body { font-family: 'Arial', sans-serif; margin: 0; padding: 0; background-color: #f9f9f9; color: #333; font-size: 16px; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 30px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.05); }
        .header { text-align: center; }
        .logo { max-width: 100px; height: auto; user-select: none; }
        .content { line-height: 1.8; font-size: 18px; }
        .footer { text-align: center; padding: 20px 0; font-size: 14px; color: #999; }
        .verification-code { 
          font-size: 40px; 
          font-weight: bold; 
          text-align: center; 
          letter-spacing: 8px; 
          margin: 35px 0; 
          padding: 20px; 
          border-radius: 8px; 
          color: #9333ea;
          border: 2px solid #f0f0f0;
        }
        .title-gradient {
          color: #9333ea;
          font-size: 32px;
          font-weight: bold;
        }
        p { margin: 16px 0; }
        a { color: #9333ea; text-decoration: none; }
        a:hover { text-decoration: underline; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <img src="https://www.woodcarvings.art/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fremoveai-removebg-preview.a7bc23d7.png&w=640&q=75" alt="removeai Logo" class="logo">
          <h1 class="title-gradient">Verification Code</h1>
        </div>
        
        <div class="content">
          <p>${t.emailTemplate.greeting}</p>
          
          <p>${t.emailTemplate.verificationCodeRequest}</p>
          
          <div class="verification-code">${verificationCode}</div>
          
          <p>${t.emailTemplate.codeExpiration}</p>
          
          <p>${t.emailTemplate.regards}<br>${t.emailTemplate.team}</p>
        </div>
        
        <div class="footer">
          <p>© ${new Date().getFullYear()} removeai. ${
    t.emailTemplate.rights
  }</p>
          <p>${t.emailTemplate.autoMessage}</p>
          <p><a href="https://removeai.com/contact">${
            t.emailTemplate.support
          }</a> | <a href="https://www.woodcarvings.art/legal/privacy-policy">${
    t.emailTemplate.privacy
  }</a></p>
        </div>
      </div>
    </body>
    </html>
  `
}
