'use client'
//
import { zodResolver } from '@hookform/resolvers/zod'
import { useRouter } from '@shared/hooks/router'
import { Button } from '@ui/components/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@ui/components/form'
import { Input } from '@ui/components/input'
import { Clock, Download, Database } from 'lucide-react'
import { useToast } from '@ui/hooks/use-toast'
import { useTranslations } from 'next-intl'
import { useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { useUser } from '../hooks/use-user'
import { SocialSigninButton, oAuthProviders } from './SocialSigninButton'
import TurnstileWidget from '@shared/components/TurnstileWidget'

export function LoginForm({
  needBackground = true,
}: {
  needBackground?: boolean
}) {
  const t = useTranslations()
  const { toast } = useToast()

  type FormValues = z.infer<typeof formSchema>
  const formSchema = z.object({
    email: z.string().email({ message: t('auth.login.invalidEmail') }),
    verificationCode: z
      .string()
      .min(1, { message: t('auth.login.emptyVerificationCode') }),
  })
  const router = useRouter()
  const { user, loaded } = useUser()
  const [showEmailForm, setShowEmailForm] = useState(false)
  const [isSending, setIsSending] = useState(false)
  const [turnstileToken, setTurnstileToken] = useState<string>('')
  const searchParams = useSearchParams()

  // 新增状态
  const [showTurnstile, setShowTurnstile] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [isVerifying, setIsVerifying] = useState(false)
  const [isSigning, setIsSigning] = useState(false)
  const [formEmail, setFormEmail] = useState('')

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    mode: 'onChange', // 启用onChange验证模式
  })

  const invitationCode = searchParams.get('invitationCode')
  const redirectTo = invitationCode
    ? `/team/invitation?code=${invitationCode}`
    : searchParams.get('redirectTo') ?? '/app'
  const email = searchParams.get('email')

  useEffect(() => {
    if (email) {
      form.setValue('email', email)
    }
  }, [email])

  // 倒计时逻辑
  useEffect(() => {
    let timer: NodeJS.Timeout | null = null
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000)
    }
    return () => {
      if (timer) clearTimeout(timer)
    }
  }, [countdown])

  const handleBotVerify = async (token: string) => {
    try {
      const response = await fetch('/api/auth/bot-verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...form.getValues(),
          turnstileToken: token,
        }),
      })

      const data = await response.json()
      if (data.code === 0) {
        console.log('Verification successful:', data)
        handleSendEmail()
      }
    } catch (error) {
      console.error('submission error:', error)
    }
  }

  const handleSendEmail = async () => {
    try {
      setIsSending(true)

      const response = await fetch('/api/auth/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: formEmail,
          subject: `removeai - ${t('auth.login.securityCode')}`,
        }),
      })

      const data = await response.json()

      if (data.code === 0) {
        toast({
          variant: 'success',
          title: t('auth.login.sendSuccess'),
        })
        // 在开发环境下，可以显示验证码以便测试
        if (process.env.NODE_ENV === 'development' && data.verificationCode) {
          console.log('开发环境验证码:', data.verificationCode)
        }

        setCountdown(60) // 设置倒计时为60秒
        setShowTurnstile(false) // 隐藏Turnstile组件
      } else {
        toast({
          title: t('auth.login.sendFailed'),
          variant: 'error',
        })
      }
    } catch (error) {
      console.error('发送邮件失败:', error)
      toast({
        title: t('auth.login.sendFailed'),
        variant: 'error',
      })
    } finally {
      setIsSending(false)
    }
  }

  // turnstile验证成功后发送验证码
  useEffect(() => {
    if (turnstileToken) {
      setTimeout(() => {
        setIsVerifying(false) // 重置验证状态
        toggleEmailForm()
      }, 2000)
      handleBotVerify(turnstileToken)
    }
  }, [turnstileToken])

  const handleRedirect = () => {
    router.replace(redirectTo)
  }

  // redirect when user has been loaded
  useEffect(() => {
    if (user && loaded) {
      handleRedirect()
    }
  }, [user, loaded])

  const onSubmit = async (e: Event) => {
    e.preventDefault()
    const values = form.getValues()
    console.log('表单值:', values)
    if (!values.verificationCode) {
      form.setError('verificationCode', {
        type: 'custom',
        message: t('auth.login.emptyVerificationCode'),
      })
      return
    }
    setIsSigning(true)
    const res = await fetch('/api/auth/verify-code', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: formEmail,
        code: values.verificationCode,
      }),
    })
    const data = await res.json()
    setIsSigning(false)
    if (data.code === 1) {
      toast({
        title: t('auth.login.wrongSecurityCode'),
        variant: 'error',
      })
      return
    }
    if (data.code === 2) {
      toast({
        title: t('auth.login.expiredSecurityCode'),
        variant: 'error',
      })
      return
    }
    if (data.code === 0) {
      handleSignIn(values.email)
    }
  }

  const handleSignIn = async (email: string) => {
    try {
      setIsSigning(true)
      const response = await fetch('/api/auth/sign-in', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formEmail,
        }),
      })
      const res = await response.json()
      if (res.code === 0) {
        toast({
          title: t('auth.login.successfulLogin'),
          variant: 'success',
        })
        // handleRedirect()
        //
        location.reload()
      }
    } catch (error) {
      console.error('登录失败:', error)
    } finally {
      setIsSigning(false)
    }
  }

  const toggleEmailForm = () => {
    setShowEmailForm(!showEmailForm)
    // 重置表单
    form.reset()
    setShowTurnstile(false)
  }

  const handleEmail = () => {
    // 先验证邮箱
    const email = form.getValues('email')
    if (!email) {
      form.setError('email', {
        type: 'custom',
        message: t('auth.login.emailRequired'),
      })
      return
    }
    setFormEmail(email)
    // 验证邮箱格式 - 使用form的触发验证
    form.trigger('email').then((isValid) => {
      if (isValid) {
        // 设置验证中状态
        setIsVerifying(true)
        // 显示Turnstile组件进行验证
        setShowTurnstile(true)
      }
    })
  }

  return (
    <div
      className={`rounded-2xl p-6 ${
        needBackground
          ? 'bg-gray-800/50 backdrop-blur-lg border border-gray-700/50'
          : ''
      }`}
    >
      {showEmailForm ? (
        // 邮箱登录表单
        <div className="space-y-4">
          <div className=" items-center font-bold text-lg sm:text-xl text-purple-500">
            {t('enterReceivedVerificationCode')}:{' '}
            <span className="text-purple-700 text-lg">{formEmail}</span>
          </div>

          <Form {...form}>
            <form className="space-y-3">
              {/* 添加验证码输入框 */}
              <FormField
                control={form.control}
                name="verificationCode"
                render={({ field }) => (
                  <FormItem className="mb-1">
                    <FormControl>
                      <Input
                        placeholder={t('auth.login.enterVerificationCode')}
                        disabled={isSending}
                        autoComplete="off"
                        className="h-10"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button
                disabled={countdown > 0 || isVerifying || isSending}
                onClick={handleSendEmail}
                loading={isSending}
                className="w-full py-1"
                variant="link"
              >
                {countdown > 0
                  ? `${t('resendVerifyCode')}(${countdown}s)`
                  : t('resendVerifyCode')}
              </Button>
              <Button
                className="w-full !mt-2 text-base font-medium h-10 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-none transition-all duration-300 transform hover:scale-[1.01] hover:shadow-md rounded-full"
                loading={isSigning}
                onClick={onSubmit}
              >
                {t('auth.verifyOtp.submit')}
              </Button>
              <Button
                className="w-full py-1"
                variant="link"
                onClick={toggleEmailForm}
              >
                {t('useAnotherEmail')}
              </Button>
            </form>
          </Form>
        </div>
      ) : (
        // 原始登录选项界面
        <>
          <div className="space-y-2 mb-5">
            <h1 className="font-bold text-2xl sm:text-3xl md:text-4xl text-purple-500">
              {t('auth.login.title')}
            </h1>
            <p className="text-lg text-gray-400">{t('auth.login.subtitle')}</p>
          </div>

          <div className="space-y-4 mb-5">
            <div className="flex items-start space-x-3">
              <div className="bg-purple-500/10 p-2 rounded-lg">
                <Clock className="w-5 h-5 text-purple-400" />
              </div>
              <div>
                <h3 className="font-medium text-white">
                  {t('moreFreeCredits')}
                </h3>
                <p className="text-sm text-gray-400">
                  {t('getAdditionalCredits')}
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="bg-pink-500/10 p-2 rounded-lg">
                <Download className="w-5 h-5 text-pink-400" />
              </div>
              <div>
                <h3 className="font-medium text-white">{t('downloadMusic')}</h3>
                <p className="text-sm text-gray-400">{t('saveAndDownload')}</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="bg-purple-500/10 p-2 rounded-lg">
                <Database className="w-5 h-5 text-purple-400" />
              </div>
              <div>
                <h3 className="font-medium text-white">
                  {t('permanentStorage')}
                </h3>
                <p className="text-sm text-gray-400">{t('safeStorage')}</p>
              </div>
            </div>
          </div>

          <div className="w-full space-y-3">
            {Object.keys(oAuthProviders).map((providerId) => (
              <SocialSigninButton
                key={providerId}
                provider={providerId}
                className="h-10"
              />
            ))}
          </div>
          <div className="relative mt-4 flex items-center">
            <div className="flex-grow border-t border-gray-700/50"></div>
            <span className="flex-shrink-0 mx-4 text-gray-400 text-sm">
              {t('or_sign_up_with_email')}
            </span>
            <div className="flex-grow border-t border-gray-700/50"></div>
          </div>

          <div className="flex justify-center flex-col mt-3 gap-2">
            <div>
              <Input
                placeholder={t('email_address')}
                className={`w-full h-10 bg-gray-800/50 border border-gray-700/50 focus:ring-2 focus:ring-purple-500 focus:border-transparent text-base font-medium placeholder:text-gray-400 transition-all duration-300 transform hover:scale-[1.01] hover:shadow-md ${
                  form.formState.errors.email?.message ? 'border-red-800' : ''
                }`}
                {...form.register('email', {
                  required: t('auth.login.emailRequired'),
                  validate: (value) =>
                    z
                      .string()
                      .email({ message: t('auth.login.invalidEmail') })
                      .safeParse(value).success || t('auth.login.invalidEmail'),
                })}
              />
              {form.formState.errors.email?.message && (
                <span className="text-red-800 text-xs font-medium mt-1 pl-2 flex">
                  {form.formState.errors.email?.message}
                </span>
              )}
              {showTurnstile && (
                <div className="mt-3">
                  <TurnstileWidget onVerify={setTurnstileToken} theme="dark" />
                </div>
              )}
            </div>

            <Button
              className="w-full text-base font-medium h-10"
              loading={isVerifying}
              onClick={handleEmail}
            >
              {t('sign_up')}
            </Button>

            <div className="text-center">
              <span className="text-xs text-gray-400">
                {t('auth.login.autoRegister')}
              </span>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
